import { NextRequest, NextResponse } from 'next/server';
import { saveTranscript, TranscriptMetadata } from '../../../../lib/utils/transcriptUtils';
import { TranscriptMessage } from '../../../../components/PMO/TranscriptPanel';
import {
  generateMeetingDocument,
  determineMeetingDocumentCategory,
  MeetingDocumentGenerationResult
} from '../../../../lib/tools/meetingDocumentGenerator';
import { createHmac } from 'crypto';

/**
 * Webhook endpoint for ElevenLabs post-call transcription
 * This endpoint is called by ElevenLabs after a conversation ends
 * to save the transcript and optionally generate documents
 *
 * IMPORTANT: This webhook must be configured at the workspace level in ElevenLabs
 * Go to: ElevenLabs Dashboard > Conversational AI > Settings > Post-call webhooks
 * Set URL to: https://your-domain.com/api/elevenlabs/post-call-webhook
 */
export async function POST(request: NextRequest) {
  try {
    console.log('[POST_CALL_WEBHOOK] Received post-call request from ElevenLabs');
    console.log('[POST_CALL_WEBHOOK] Request headers:', Object.fromEntries(request.headers.entries()));

    // Get request body as text for HMAC validation
    const bodyText = await request.text();
    console.log('[POST_CALL_WEBHOOK] Request body length:', bodyText.length);

    // Verify HMAC signature (ElevenLabs standard authentication)
    const signatureHeader = request.headers.get('elevenlabs-signature');
    if (signatureHeader) {
      const isValidSignature = await verifyElevenLabsSignature(bodyText, signatureHeader);
      if (!isValidSignature) {
        console.error('[POST_CALL_WEBHOOK] Invalid HMAC signature');
        return NextResponse.json({ error: 'Invalid signature' }, { status: 401 });
      }
      console.log('[POST_CALL_WEBHOOK] HMAC signature verified successfully');
    } else {
      // Fallback to Bearer token authentication for testing
      const authHeader = request.headers.get('authorization');
      const expectedAuth = `Bearer ${process.env.ELEVENLABS_WEBHOOK_SECRET || 'pmo-webhook-secret-2024'}`;

      // Allow multiple auth tokens for testing and production flexibility
      const validAuthTokens = [
        expectedAuth,
        'Bearer pmo-webhook-secret-2024',
        'Bearer default-secret',
        `Bearer ${process.env.INTERNAL_API_SECRET}`
      ].filter(Boolean);

      if (!authHeader || !validAuthTokens.includes(authHeader)) {
        console.error('[POST_CALL_WEBHOOK] No valid authentication found');
        console.error('[POST_CALL_WEBHOOK] Received auth:', authHeader);
        console.error('[POST_CALL_WEBHOOK] Expected signature header or one of:', validAuthTokens);
        return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
      }
    }

    // Parse request body
    const body = JSON.parse(bodyText);
    console.log('[POST_CALL_WEBHOOK] Request body:', JSON.stringify(body, null, 2));

    // Extract event type and data (ElevenLabs format)
    const { type: event_type, data } = body;

    if (event_type !== 'post_call_transcription') {
      console.log('[POST_CALL_WEBHOOK] Ignoring non-transcription event:', event_type);
      return NextResponse.json({
        message: 'Event type not handled',
        event_type
      }, { status: 200 });
    }

    // Extract conversation data (ElevenLabs post-call format)
    const {
      transcript,
      agent_id,
      conversation_id,
      metadata = {},
      analysis = {},
      conversation_initiation_client_data = {}
    } = data;

    // Extract metadata from ElevenLabs structure
    const user_metadata = conversation_initiation_client_data.dynamic_variables || {};
    const conversation_metadata = {
      agent_name: 'Marketing Director', // Default from the conversation
      agent_type: 'PMO',
      start_time: metadata.start_time_unix_secs ? new Date(metadata.start_time_unix_secs * 1000).toISOString() : undefined,
      end_time: metadata.start_time_unix_secs && metadata.call_duration_secs ?
        new Date((metadata.start_time_unix_secs + metadata.call_duration_secs) * 1000).toISOString() : undefined,
      call_duration: metadata.call_duration_secs,
      transcript_summary: analysis.transcript_summary,
      call_successful: analysis.call_successful
    };

    const start_time = conversation_metadata.start_time;
    const end_time = conversation_metadata.end_time;
    const web_search_results: any[] = []; // ElevenLabs doesn't include this in post-call data
    const selected_document_categories: string[] = [];

    console.log('[POST_CALL_WEBHOOK] Processing transcription:', {
      agent_id,
      conversation_id,
      transcript_length: transcript?.length || 0,
      user_metadata,
      conversation_metadata
    });

    // Validate required data
    if (!transcript || !agent_id || !conversation_id) {
      console.error('[POST_CALL_WEBHOOK] Missing required data:', {
        has_transcript: !!transcript,
        has_agent_id: !!agent_id,
        has_conversation_id: !!conversation_id
      });
      return NextResponse.json({
        error: 'Missing required data: transcript, agent_id, and conversation_id are required'
      }, { status: 400 });
    }

    // Map ElevenLabs transcript format to our internal format
    const transcriptMessages: TranscriptMessage[] = mapElevenLabsTranscript(transcript);

    if (transcriptMessages.length === 0) {
      console.warn('[POST_CALL_WEBHOOK] No valid transcript messages found');
      return NextResponse.json({
        message: 'No transcript content to save'
      }, { status: 200 });
    }

    // Extract metadata for transcript saving
    const agentName = conversation_metadata.agent_name || (user_metadata as any).agent_name || 'PMO Agent';
    const agentType = conversation_metadata.agent_type || (user_metadata as any).agent_type || 'PMO';
    const userId = (user_metadata as any).user_id || metadata.user_id || 'system';
    const category = (user_metadata as any).category || 'Meeting Transcript';

    // Create transcript metadata
    const transcriptMetadata: TranscriptMetadata = {
      agentName,
      agentType,
      documentTitle: `Meeting Transcript - ${agentName} - ${new Date().toLocaleDateString()}`,
      category,
      startTime: start_time ? new Date(start_time) : new Date(Date.now() - (transcriptMessages.length * 30000)), // Estimate if not provided
      endTime: end_time ? new Date(end_time) : new Date(),
      totalMessages: transcriptMessages.length,
      userMessages: transcriptMessages.filter(m => m.role === 'user').length,
      agentMessages: transcriptMessages.filter(m => m.role === 'assistant' || m.role === 'agent').length
    };

    console.log('[POST_CALL_WEBHOOK] Saving transcript with metadata:', transcriptMetadata);

    // Save transcript using existing utility
    const saveResult = await saveTranscript(transcriptMessages, {
      userId,
      agentId: agent_id,
      metadata: transcriptMetadata,
      uploadToKnowledgeBase: true, // Always upload to knowledge base for future reference
      forceUpload: false,
      onProgress: (step: string, message: string, progress?: number) => {
        console.log(`[POST_CALL_WEBHOOK] Progress - ${step}: ${message} (${progress || 0}%)`);
      }
    });

    if (!saveResult.success) {
      console.error('[POST_CALL_WEBHOOK] Failed to save transcript:', saveResult.error);
      return NextResponse.json({
        error: 'Failed to save transcript',
        details: saveResult.error
      }, { status: 500 });
    }

    console.log('[POST_CALL_WEBHOOK] Transcript saved successfully:', {
      pdfUrl: saveResult.pdfUrl,
      fileName: saveResult.fileName,
      knowledgeBaseId: saveResult.knowledgeBaseId
    });

    // Handle document generation requests (Task 2B)
    // Check if document generation was requested during the meeting
    // For now, auto-generate document based on conversation content
    const documentGenerationRequested = conversation_metadata.transcript_summary &&
                                      conversation_metadata.transcript_summary.length > 50; // Auto-generate if substantial content

    const documentTitle = (user_metadata as any).document_title ||
                         `Meeting Summary - ${agentName} - ${new Date().toLocaleDateString()}`;

    let documentGenerationResult: MeetingDocumentGenerationResult | null = null;

    if (documentGenerationRequested) {
      try {
        console.log('[POST_CALL_WEBHOOK] Document generation requested:', {
          title: documentTitle,
          hasWebSearchResults: web_search_results.length > 0,
          selectedCategories: selected_document_categories
        });

        // Determine appropriate category based on meeting context
        const documentCategory = determineMeetingDocumentCategory(
          selected_document_categories,
          transcriptMessages.map(m => m.content).join(' ')
        );

        // Generate the meeting document
        documentGenerationResult = await generateMeetingDocument({
          title: documentTitle,
          category: documentCategory,
          meetingTranscript: transcriptMessages,
          agentId: agent_id,
          userId,
          webSearchResults: web_search_results,
          additionalContext: (user_metadata as any).additional_context || conversation_metadata.transcript_summary,
          onProgress: (step: string, message: string, progress?: number) => {
            console.log(`[POST_CALL_WEBHOOK] Document Generation - ${step}: ${message} (${progress || 0}%)`);
          }
        });

        if (documentGenerationResult.success) {
          console.log('[POST_CALL_WEBHOOK] Document generated successfully:', {
            documentId: documentGenerationResult.documentId,
            downloadUrl: documentGenerationResult.downloadUrl,
            knowledgeBaseId: documentGenerationResult.knowledgeBaseId
          });
        } else {
          console.error('[POST_CALL_WEBHOOK] Document generation failed:', documentGenerationResult.error);
        }

      } catch (docGenError) {
        console.error('[POST_CALL_WEBHOOK] Error during document generation:', docGenError);
        documentGenerationResult = {
          success: false,
          error: docGenError instanceof Error ? docGenError.message : 'Unknown error during document generation'
        };
      }
    }

    return NextResponse.json({
      success: true,
      message: 'Post-call processing completed successfully',
      results: {
        transcript: {
          saved: true,
          pdfUrl: saveResult.pdfUrl,
          fileName: saveResult.fileName,
          knowledgeBaseId: saveResult.knowledgeBaseId
        },
        documentGeneration: documentGenerationResult
      }
    });

  } catch (error) {
    console.error('[POST_CALL_WEBHOOK] Error processing post-call webhook:', error);
    return NextResponse.json({
      error: 'Internal server error',
      details: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 });
  }
}

/**
 * Map ElevenLabs transcript format to our internal TranscriptMessage format
 * ElevenLabs format: [{ role: 'agent'|'user', message: string, time_in_call_secs: number }]
 */
function mapElevenLabsTranscript(transcript: any): TranscriptMessage[] {
  try {
    if (!transcript) return [];

    // Handle different possible transcript formats
    let messages: any[] = [];

    if (Array.isArray(transcript)) {
      messages = transcript;
    } else if (transcript.messages && Array.isArray(transcript.messages)) {
      messages = transcript.messages;
    } else if (transcript.turns && Array.isArray(transcript.turns)) {
      messages = transcript.turns;
    } else if (typeof transcript === 'string') {
      // If transcript is a string, try to parse it
      try {
        const parsed = JSON.parse(transcript);
        if (Array.isArray(parsed)) {
          messages = parsed;
        } else if (parsed.messages) {
          messages = parsed.messages;
        }
      } catch {
        // If parsing fails, treat as single message
        return [{
          role: 'assistant',
          content: transcript,
          timestamp: new Date()
        }];
      }
    }

    return messages.map((message: any, index: number) => {
      // Map ElevenLabs message format
      const role = mapRole(message.role || message.speaker || message.type);
      const content = message.message || message.content || message.text || '';

      // Calculate timestamp based on time_in_call_secs if available
      let timestamp: Date;
      if (message.time_in_call_secs !== undefined) {
        // Estimate call start time and add seconds
        const estimatedStartTime = Date.now() - (messages.length * 30000); // Rough estimate
        timestamp = new Date(estimatedStartTime + (message.time_in_call_secs * 1000));
      } else if (message.timestamp) {
        timestamp = new Date(message.timestamp);
      } else {
        // Fallback: estimate based on message order
        timestamp = new Date(Date.now() - ((messages.length - index) * 30000));
      }

      return {
        role,
        content,
        timestamp
      };
    }).filter(message => message.content.trim().length > 0);

  } catch (error) {
    console.error('[POST_CALL_WEBHOOK] Error mapping transcript:', error);
    return [];
  }
}

/**
 * Map ElevenLabs role names to our internal role names
 */
function mapRole(role: string): string {
  if (!role) return 'assistant';

  const normalizedRole = role.toLowerCase();

  if (normalizedRole.includes('user') || normalizedRole.includes('human') || normalizedRole.includes('customer')) {
    return 'user';
  }

  if (normalizedRole.includes('agent') || normalizedRole.includes('assistant') || normalizedRole.includes('ai')) {
    return 'assistant';
  }

  // Default to assistant for unknown roles
  return 'assistant';
}

/**
 * Verify ElevenLabs HMAC signature
 * Format: t=timestamp,v0=hash
 */
async function verifyElevenLabsSignature(payload: string, signatureHeader: string): Promise<boolean> {
  try {
    const secret = process.env.ELEVENLABS_WEBHOOK_SECRET || 'pmo-webhook-secret-2024';

    // Parse signature header
    const parts = signatureHeader.split(',');
    let timestamp = '';
    let signature = '';

    for (const part of parts) {
      const [key, value] = part.split('=');
      if (key === 't') {
        timestamp = value;
      } else if (key === 'v0') {
        signature = value;
      }
    }

    if (!timestamp || !signature) {
      console.error('[POST_CALL_WEBHOOK] Invalid signature header format');
      return false;
    }

    // Validate timestamp (within 30 minutes)
    const currentTime = Math.floor(Date.now() / 1000);
    const requestTime = parseInt(timestamp);
    const tolerance = 30 * 60; // 30 minutes

    if (currentTime - requestTime > tolerance) {
      console.error('[POST_CALL_WEBHOOK] Request timestamp too old');
      return false;
    }

    // Create HMAC signature
    const fullPayload = `${timestamp}.${payload}`;
    const expectedSignature = createHmac('sha256', secret)
      .update(fullPayload, 'utf8')
      .digest('hex');

    // Compare signatures
    return signature === expectedSignature;

  } catch (error) {
    console.error('[POST_CALL_WEBHOOK] Error verifying signature:', error);
    return false;
  }
}
