import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth/next';
import { authOptions } from '../../auth/[...nextauth]/authOptions';
import { ElevenLabsClient } from 'elevenlabs';
import { checkPMOAgentExists, validateAndFixUserAgentConfig, applyAgentConfigurationFix } from '../../../../lib/elevenlabs/agentValidation';
import { storeUserAgent, updateUserAgent } from '../../../../lib/firebase/userAgents';
import { uploadToKnowledgeBaseWithDeduplication } from '../../../../components/scriptreaderAI/uploadKnowledgebase';
import { updateAgentKnowledgeBase } from '../../../../components/scriptreaderAI/elevenlabs';

interface CreateAgentRequest {
  agentId: string;
  name: string;
  voiceId: string;
  prompt: string;
  knowledgeBase?: Array<{
    id: string;
    title: string;
    content: string;
    metadata?: Record<string, any>;
  }>;
}

/**
 * Process documents for an agent (both new and existing agents)
 */
async function processDocumentsForAgent(
  agentId: string,
  knowledgeBase: Array<{
    id: string;
    title: string;
    content: string;
    metadata?: Record<string, any>;
  }>,
  apiKey: string
): Promise<number> {
  let documentsUploaded = 0;

  if (knowledgeBase.length === 0) {
    return documentsUploaded;
  }

  try {
    console.log(`[ELEVENLABS_AGENT] Processing ${knowledgeBase.length} documents for agent: ${agentId}`);

    // Process each document using the proper upload functions
    for (const doc of knowledgeBase) {
      try {
        console.log(`[ELEVENLABS_AGENT] Uploading document: ${doc.title}`);

        // Create a temporary file URL for the document content
        // Since we have text content, we'll create a blob URL
        const blob = new Blob([doc.content], { type: 'text/plain' });
        const tempFileUrl = URL.createObjectURL(blob);

        // Upload document with deduplication
        const uploadResult = await uploadToKnowledgeBaseWithDeduplication(
          tempFileUrl,
          doc.title,
          'text/plain',
          apiKey,
          false // Don't force upload, use deduplication
        );

        console.log(`[ELEVENLABS_AGENT] Upload result for ${doc.title}:`, uploadResult);

        // Associate document with agent (this will handle RAG indexing internally)
        console.log(`[ELEVENLABS_AGENT] Associating document with agent: ${agentId}`);
        try {
          const agentUpdateResult = await updateAgentKnowledgeBase(
            agentId,
            uploadResult.id,
            apiKey
          );
          console.log(`[ELEVENLABS_AGENT] Agent update result for ${doc.title}:`, agentUpdateResult);

          // Verify the association was successful
          if (agentUpdateResult && !agentUpdateResult.error) {
            documentsUploaded++;
            console.log(`[ELEVENLABS_AGENT] Successfully processed document: ${doc.title}`);
          } else {
            console.error(`[ELEVENLABS_AGENT] Failed to associate document ${doc.title} with agent: ${agentUpdateResult?.error || 'Unknown error'}`);
          }
        } catch (agentUpdateError) {
          console.error(`[ELEVENLABS_AGENT] Error updating agent for ${doc.title}:`, agentUpdateError);
          // Don't increment documentsUploaded if association failed
        }

        // Clean up the temporary URL
        URL.revokeObjectURL(tempFileUrl);

      } catch (docError) {
        console.error(`[ELEVENLABS_AGENT] Error processing document ${doc.title}:`, docError);
        // Continue with other documents
      }
    }

    console.log(`[ELEVENLABS_AGENT] Completed document processing: ${documentsUploaded}/${knowledgeBase.length} documents successfully associated with agent`);

  } catch (kbError) {
    console.error('[ELEVENLABS_AGENT] Error setting up knowledge base:', kbError);
    // Continue without knowledge base - agent will still work
  }

  return documentsUploaded;
}

/**
 * API endpoint for creating ElevenLabs conversational agents for PMO voice meetings
 */
export async function POST(request: NextRequest) {
  try {
    // Check authentication
    const session = await getServerSession(authOptions);
    if (!session?.user?.email) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const userId = session.user.email;

    if (!userId) {
      return NextResponse.json({
        success: false,
        error: 'User email is required'
      }, { status: 400 });
    }

    // Parse request body
    const requestBody = await request.json();
    console.log(`[ELEVENLABS_AGENT] Received request body:`, JSON.stringify(requestBody, null, 2));

    const {
      agentId,
      name,
      voiceId,
      prompt,
      knowledgeBase = []
    }: CreateAgentRequest = requestBody;

    console.log(`[ELEVENLABS_AGENT] Parsed parameters:`, {
      agentId,
      name,
      voiceId,
      promptLength: prompt?.length || 0,
      knowledgeBaseCount: knowledgeBase?.length || 0
    });

    // Validate required parameters
    if (!agentId || !name || !voiceId || !prompt) {
      console.error(`[ELEVENLABS_AGENT] Missing required parameters:`, {
        hasAgentId: !!agentId,
        hasName: !!name,
        hasVoiceId: !!voiceId,
        hasPrompt: !!prompt
      });
      return NextResponse.json({
        error: 'Missing required parameters: agentId, name, voiceId, and prompt are required'
      }, { status: 400 });
    }

    // Get ElevenLabs API key
    const apiKey = process.env.NEXT_PUBLIC_ELEVENLABS_COMPANY_API_KEY;
    if (!apiKey) {
      return NextResponse.json({
        error: 'ElevenLabs API key not configured'
      }, { status: 500 });
    }

    console.log(`[ELEVENLABS_AGENT] Processing request for user: ${userId}`);

    // Step 1: Check if this specific PMO agent already exists
    console.log(`[ELEVENLABS_AGENT] Checking for existing PMO agent: ${agentId} for user: ${userId}`);
    const existenceCheck = await checkPMOAgentExists(userId, agentId, apiKey);

    if (existenceCheck.exists && existenceCheck.userAgent && existenceCheck.elevenLabsAgent) {
      console.log(`[ELEVENLABS_AGENT] Found existing valid agent: ${existenceCheck.userAgent.agentId}`);

      // Log End Call tool status for existing agent
      if (existenceCheck.hasEndCallTool !== undefined) {
        console.log(`[ELEVENLABS_AGENT] End Call tool status for existing agent: ${existenceCheck.hasEndCallTool ? 'PRESENT' : 'MISSING'}`);
        if (existenceCheck.needsEndCallToolUpdate) {
          console.log(`[ELEVENLABS_AGENT] Legacy agent ${existenceCheck.userAgent.agentId} requires End Call tool update`);
        }
      }

      // Validate and potentially fix the agent configuration
      const configValidation = await validateAndFixUserAgentConfig(existenceCheck.userAgent.agentId, apiKey);

      if (configValidation.needsUpdate && configValidation.updatedConfig) {
        console.log(`[ELEVENLABS_AGENT] Applying configuration fixes to existing agent`);

        // Log specific End Call tool update
        if (configValidation.needsEndCallToolUpdate) {
          console.log(`[ELEVENLABS_AGENT] 🔧 LEGACY AGENT UPDATE: Adding End Call tool to agent ${existenceCheck.userAgent.agentId}`);
        }

        const fixResult = await applyAgentConfigurationFix(
          existenceCheck.userAgent.agentId,
          configValidation.updatedConfig,
          apiKey
        );

        if (!fixResult.success) {
          console.warn(`[ELEVENLABS_AGENT] Failed to apply configuration fix: ${fixResult.error}`);
        } else if (configValidation.needsEndCallToolUpdate) {
          console.log(`[ELEVENLABS_AGENT] ✅ SUCCESS: End Call tool successfully added to legacy agent ${existenceCheck.userAgent.agentId}`);
        }
      }

      // Process any new documents for the existing agent
      let documentsUploaded = 0;
      if (knowledgeBase.length > 0) {
        console.log(`[ELEVENLABS_AGENT] Processing ${knowledgeBase.length} new documents for existing agent`);
        documentsUploaded = await processDocumentsForAgent(
          existenceCheck.userAgent.agentId,
          knowledgeBase,
          apiKey
        );
      }

      // Build status message
      let statusMessage = 'Existing agent found and validated';
      if (documentsUploaded > 0) {
        statusMessage += ` with ${documentsUploaded} new documents added`;
      }
      if (configValidation.needsEndCallToolUpdate && configValidation.needsUpdate) {
        statusMessage += ' and End Call tool added';
      }

      return NextResponse.json({
        success: true,
        agentId: existenceCheck.userAgent.agentId,
        conversationId: existenceCheck.userAgent.conversationId,
        name: existenceCheck.userAgent.agentName,
        voiceId: existenceCheck.userAgent.voiceId,
        documentsUploaded: (existenceCheck.userAgent.metadata?.documentsUploaded || 0) + documentsUploaded,
        message: statusMessage,
        wasExisting: true,
        configurationIssues: configValidation.issues,
        configurationFixed: configValidation.needsUpdate && configValidation.updatedConfig ? true : false,
        endCallToolAdded: configValidation.needsEndCallToolUpdate && configValidation.needsUpdate ? true : false,
        hasEndCallTool: configValidation.hasEndCallTool || false
      });
    }

    console.log(`[ELEVENLABS_AGENT] No existing agent found in database for user-generated ID: ${agentId}. Creating new agent...`);

    // Generate the proper PMO agent name for the new agent
    const { generatePMOAgentName, getPMOAgentConfig } = await import('../../../../lib/agents/voice/pmoAgentVoiceConfig');
    const properAgentName = generatePMOAgentName(userId, name);
    const agentConfig = getPMOAgentConfig(name);
    const displayName = agentConfig?.agentName || name;
    console.log(`[ELEVENLABS_AGENT] Will create new agent with name: ${properAgentName} (display: ${displayName})`);

    console.log(`[ELEVENLABS_AGENT] Creating new agent: ${agentId} (${properAgentName})`);

    // Step 1.5: Final check - ensure no agent with this name exists in ElevenLabs
    const { verifyAgentInElevenLabsByName } = await import('../../../../lib/elevenlabs/agentValidation');
    const finalCheck = await verifyAgentInElevenLabsByName(properAgentName, apiKey);

    if (finalCheck.exists) {
      const existingAgentId = finalCheck.agentData?.agent_id as string;
      console.log(`[ELEVENLABS_AGENT] Agent with name "${properAgentName}" already exists in ElevenLabs: ${existingAgentId}`);

      // Process knowledge base for the existing agent-by-name case (previously skipped)
      let documentsUploaded = 0;
      try {
        if (knowledgeBase.length > 0) {
          console.log(`[ELEVENLABS_AGENT] Processing ${knowledgeBase.length} documents for existing agent (found by name)`);
          documentsUploaded = await processDocumentsForAgent(existingAgentId, knowledgeBase, apiKey);
        }
      } catch (kbError) {
        console.error('[ELEVENLABS_AGENT] Error processing knowledge base for existing agent:', kbError);
      }

      return NextResponse.json({
        success: true,
        agentId: existingAgentId,
        conversationId: null,
        name: properAgentName,
        voiceId,
        documentsUploaded,
        message: `Existing agent found in ElevenLabs${documentsUploaded > 0 ? ` with ${documentsUploaded} documents added` : ''}`,
        wasExisting: true,
        configurationIssues: [],
        configurationFixed: false
      });
    }

    // Step 2: Create the conversational agent using ElevenLabs SDK
    let agentData: any = null; // Declare agentData outside try block

    try {
      console.log(`[ELEVENLABS_AGENT] Initializing ElevenLabs client...`);
      const client = new ElevenLabsClient({ apiKey });

      console.log(`[ELEVENLABS_AGENT] Will add End Call system tool to agent configuration...`);

      console.log(`[ELEVENLABS_AGENT] Building agent configuration...`);

      // Build tools array - start with end_call for all agents
      const tools: any[] = [
        {
          type: 'system' as const,
          name: 'end_call',
          description: 'End the call when the user says goodbye, thank you, or indicates they have no more questions. You can only end the call after all their questions have been answered. Please end the call only after confirming that the user doesn\'t need any additional assistance.'
        }
      ];

      // Add tools using standalone tool approach for better reusability
      // Always attach webhook tools (web search + per-agent tools)
      // Note: ElevenLabs must be able to reach the webhook URL (NEXTAUTH_URL must be a public HTTPS URL)
      const enableWebhookTools = true;
      const useStandaloneTools = process.env.ELEVENLABS_USE_STANDALONE_TOOLS === 'true';

      console.log(`[ELEVENLABS_AGENT] Tool configuration:`, {
        enableWebhookTools,
        useStandaloneTools,
        standaloneToolsEnvVar: process.env.ELEVENLABS_USE_STANDALONE_TOOLS
      });

      if (enableWebhookTools) {
        if (useStandaloneTools) {
          console.log('[ELEVENLABS_AGENT] Using standalone tools approach for better reusability');

          try {
            // Import standalone tools manager
            const { getStandaloneTools, getToolsForAgent } = await import('../../../../lib/elevenlabs/standaloneTools');

            // Get or create standalone tools
            const standaloneTools = await getStandaloneTools(apiKey);

            // Get tool IDs for this specific agent
            const toolIds = getToolsForAgent(name, standaloneTools);

            // Add tool references to agent configuration
            toolIds.forEach(toolId => {
              tools.push({
                type: 'tool_reference',
                tool_id: toolId
              });
            });

            console.log(`[ELEVENLABS_AGENT] Added ${toolIds.length} standalone tools for ${name} agent:`, toolIds);

          } catch (standaloneError) {
            console.error('[ELEVENLABS_AGENT] Failed to use standalone tools, falling back to inline:', standaloneError);
            // Fall back to inline tools if standalone fails
            await addInlineTools();
          }
        } else {
          console.log('[ELEVENLABS_AGENT] Using inline tools approach (legacy)');
          await addInlineTools();
        }
      } else {
        console.log('[ELEVENLABS_AGENT] Webhook tools disabled via feature flag');
      }

      // Inline tools function for fallback compatibility
      async function addInlineTools() {
        console.log(`[ELEVENLABS_AGENT] Adding inline tools for agent: "${name}"`);

        if (name === 'DocumentationGeneration') {
          console.log(`[ELEVENLABS_AGENT] Adding web search tool for DocumentationGeneration agent`);
          // Web Search Tool - Using proper ElevenLabs webhook format
          tools.push({
            type: 'webhook',
            name: 'search_web',
            description: 'Search the internet for current information, research topics, and gather external data to enhance documentation. Use this when you need up-to-date information, industry trends, best practices, or external references.',
            disable_interruptions: false,
            force_pre_tool_speech: false,
            assignments: [],
            api_schema: {
              url: `${process.env.ELEVENLABS_WEBHOOK_BASE_URL || process.env.NEXTAUTH_URL || 'http://localhost:3000'}/api/elevenlabs/web-search-webhook`,
              method: 'POST',
              path_params_schema: {},
              query_params_schema: null,
              request_body_schema: {
                type: 'object',
                properties: {
                  query: {
                    type: 'string',
                    description: 'The search query to look up on the web. Be specific and include relevant keywords for better results.'
                  },
                  context: {
                    type: 'string',
                    description: 'Additional context about what you are searching for and how it relates to the current discussion or document being created.'
                  },
                  searchPurpose: {
                    type: 'string',
                    description: 'Purpose of the search to help filter and present relevant results'
                  }
                },
                required: ['query']
              },
              request_headers: {
                'Authorization': `Bearer ${process.env.ELEVENLABS_WEBHOOK_SECRET || 'pmo-webhook-secret-2024'}`,
                'Content-Type': 'application/json'
              },
              auth_connection: null
            },
            response_timeout_secs: 30,
            dynamic_variables: {
              dynamic_variable_placeholders: {}
            }
          });

          // Note: Document generation tool removed - using existing knowledge base update routine instead
        }

        // Add web search to ALL agents (except DocumentationGeneration which already has it)
        const webSearchEnabledAgents = [
          'Research', 'Marketing', 'BusinessAnalysis', 'InvestigativeResearch',
          'SoftwareDesign', 'Sales', 'ProjectManagement', 'QualityAssurance',
          'DataAnalysis', 'CustomerSuccess', 'Operations', 'Finance', 'Legal', 'HR'
        ];
        console.log(`[ELEVENLABS_AGENT] Checking if "${name}" is in webSearchEnabledAgents:`, webSearchEnabledAgents.includes(name));
        if (webSearchEnabledAgents.includes(name)) {
          console.log(`[ELEVENLABS_AGENT] Adding web search tool for ${name} agent`);
          tools.push({
            type: 'webhook',
            name: 'search_web',
            description: 'Search the internet for current information, research topics, and gather external data. Use this when you need up-to-date information, industry trends, best practices, or external references.',
            disable_interruptions: false,
            force_pre_tool_speech: false,
            assignments: [],
            api_schema: {
              url: `${process.env.ELEVENLABS_WEBHOOK_BASE_URL || process.env.NEXTAUTH_URL || 'http://localhost:3000'}/api/elevenlabs/web-search-webhook`,
              method: 'POST',
              path_params_schema: {},
              query_params_schema: null,
              request_body_schema: {
                type: 'object',
                properties: {
                  query: {
                    type: 'string',
                    description: 'The search query to look up on the web. Be specific and include relevant keywords for better results.'
                  },
                  context: {
                    type: 'string',
                    description: 'Additional context about what you are searching for and how it relates to the current discussion.'
                  },
                  searchPurpose: {
                    type: 'string',
                    description: 'Purpose of the search to help filter and present relevant results'
                  }
                },
                required: ['query']
              },
              request_headers: {
                'Authorization': `Bearer ${process.env.ELEVENLABS_WEBHOOK_SECRET || 'pmo-webhook-secret-2024'}`,
                'Content-Type': 'application/json'
              },
              auth_connection: null
            },
            response_timeout_secs: 30,
            dynamic_variables: {
              dynamic_variable_placeholders: {}
            }
          });
        }

        // Add document generation tool to PMO agents
        const documentGenerationEnabledAgents = [
          'ProjectManagement', 'BusinessAnalysis', 'Research', 'Marketing',
          'Operations', 'QualityAssurance', 'DataAnalysis'
        ];

        if (documentGenerationEnabledAgents.includes(name)) {
          console.log(`[ELEVENLABS_AGENT] Adding document generation tool for ${name} agent`);
          tools.push({
            type: 'webhook',
            name: 'generate_meeting_document',
            description: 'Generate a professional document based on our meeting discussion and any web search results. Use this when the user requests document creation or when you identify an opportunity to create valuable documentation from our conversation.',
            disable_interruptions: false,
            force_pre_tool_speech: false,
            assignments: [],
            api_schema: {
              url: `${process.env.ELEVENLABS_WEBHOOK_BASE_URL || process.env.NEXTAUTH_URL || 'http://localhost:3000'}/api/elevenlabs/generate-meeting-document-webhook`,
              method: 'POST',
              path_params_schema: {},
              query_params_schema: null,
              request_body_schema: {
                type: 'object',
                properties: {
                  title: {
                    type: 'string',
                    description: 'The title for the document to be generated. Should be descriptive and professional.'
                  },
                  category: {
                    type: 'string',
                    description: 'The category for the document (e.g., Strategic Planning, Requirements, Assessment, Research, General). If not specified, will be auto-determined.'
                  },
                  additional_context: {
                    type: 'string',
                    description: 'Any additional context or specific requirements for the document generation.'
                  }
                },
                required: ['title']
              },
              request_headers: {
                'Authorization': `Bearer ${process.env.ELEVENLABS_WEBHOOK_SECRET || 'pmo-webhook-secret-2024'}`,
                'Content-Type': 'application/json'
              },
              auth_connection: null
            },
            response_timeout_secs: 60, // Longer timeout for document generation
            dynamic_variables: {
              dynamic_variable_placeholders: {}
            }
          });
        }

        // Add save_meeting_summary tool for ALL agents to handle post-call processing
        console.log(`[ELEVENLABS_AGENT] Adding save_meeting_summary tool for ${name} agent`);
        tools.push({
          type: 'webhook',
          name: 'save_meeting_summary',
          description: 'Save the meeting transcript and generate a summary document. Use this tool ONLY when the user explicitly requests to save the meeting transcript, create a meeting summary, or document the conversation. Do not use this tool automatically or without user request.',
          disable_interruptions: false,
          force_pre_tool_speech: false,
          assignments: [],
          api_schema: {
            url: `${process.env.ELEVENLABS_WEBHOOK_BASE_URL || process.env.NEXTAUTH_URL || 'http://localhost:3000'}/api/elevenlabs/save-meeting-summary-webhook`,
            method: 'POST',
            path_params_schema: {},
            query_params_schema: null,
            request_body_schema: {
              type: 'object',
              properties: {
                summary: {
                  type: 'string',
                  description: 'A brief summary of the key points discussed in the meeting.'
                },
                action_items: {
                  type: 'string',
                  description: 'Any action items or next steps identified during the meeting.'
                },
                document_title: {
                  type: 'string',
                  description: 'Optional title for the meeting summary document. If not provided, will be auto-generated.'
                },
                generate_document: {
                  type: 'boolean',
                  description: 'Whether to generate a comprehensive meeting document. Defaults to true.'
                },
                web_search_results: {
                  type: 'array',
                  description: 'Array of web search results obtained during the meeting. Include all search results from search_web tool calls.',
                  items: {
                    type: 'object',
                    properties: {
                      title: {
                        type: 'string',
                        description: 'Title of the search result'
                      },
                      snippet: {
                        type: 'string',
                        description: 'Snippet or summary of the search result'
                      },
                      link: {
                        type: 'string',
                        description: 'URL link to the search result'
                      },
                      query: {
                        type: 'string',
                        description: 'The search query that produced this result'
                      }
                    }
                  }
                },
                search_queries: {
                  type: 'array',
                  description: 'Array of search queries that were performed during the meeting.',
                  items: {
                    type: 'string',
                    description: 'A search query performed during the meeting'
                  }
                },
                research_findings: {
                  type: 'string',
                  description: 'Summary of key research findings and insights discovered through web searches during the meeting.'
                },
                // ✅ Add agent context fields to be populated by ElevenLabs
                agent_id: {
                  type: 'string',
                  description: 'The ID of the agent calling this webhook (automatically populated by ElevenLabs)'
                },
                conversation_id: {
                  type: 'string',
                  description: 'The ID of the current conversation (automatically populated by ElevenLabs)'
                },
                agent_name: {
                  type: 'string',
                  description: 'The name of the agent (automatically populated by ElevenLabs)'
                },
                // ✅ Add transcript metadata for agent type and document context
                transcript_metadata: {
                  type: 'object',
                  description: 'Metadata about the conversation context including agent type and document categories',
                  properties: {
                    agentType: {
                      type: 'string',
                      description: 'The type of agent (Marketing, Research, Sales, SoftwareDesign, BusinessAnalysis) based on the conversation context and agent role'
                    },
                    documentCategory: {
                      type: 'string',
                      description: 'The category of documents being discussed or referenced in the conversation'
                    },
                    selectedDocuments: {
                      type: 'array',
                      description: 'Array of document IDs or titles that were referenced or discussed during the conversation',
                      items: {
                        type: 'string'
                      }
                    }
                  }
                }
              },
              required: ['summary']
            },
            request_headers: {
              'Authorization': `Bearer ${process.env.ELEVENLABS_WEBHOOK_SECRET || 'pmo-webhook-secret-2024'}`,
              'Content-Type': 'application/json'
            },
            auth_connection: null
          },
          response_timeout_secs: 60, // Timeout for PDF generation (KB upload happens in background)
          dynamic_variables: {
            // ✅ Configure dynamic variables to inject agent context
            dynamic_variable_placeholders: {
              agent_id: '{{system__agent_id}}',
              conversation_id: '{{system__conversation_id}}',
              agent_name: '{{system__agent_name}}'
            }
          }
        });
      }

      // Debug: Log the final tools array before creating agent config
      console.log(`[ELEVENLABS_AGENT] Final tools array before agent creation (${tools.length} tools):`,
        tools.map(t => ({ type: t.type, name: t.name || 'unnamed' })));

      const agentConfig = {
        name: properAgentName, // Use the proper PMO agent name
        // Note: agent_id removed as it's not used in working userAgentManager
        conversation_config: {
          agent: {
            prompt: {
              prompt: prompt,
              tools: tools
            },
            // Use Gemini 2.5 Flash for optimal performance (as per working example)
            llm: {
              model: "gpt-o3-2025-04-16",
              "temperature": 0.5
            },
            language: 'en',
            first_message: `Hello! I'm ${displayName}, ready to discuss projects with you.`
          },
          tts: {
            voice_id: voiceId,
            // Note: model_id removed to use ElevenLabs default (following working userAgentManager pattern)
            stability: 0.5,
            speed: 1.0,
            similarity_boost: 0.8,
            optimize_streaming_latency: 4 // Maximum responsiveness
          },
          conversation: {
            max_duration_seconds: 1800, // 30 minutes
            text_only: false,
            // CRITICAL: Enable audio and text response transmission over WebSocket
            client_events: [
              "conversation_initiation_metadata" as const,
              "agent_response" as const,
              "user_transcript" as const,
              "audio" as const // CRITICAL: Enable audio streaming to client
            ]
          },
          turn: {
            turn_timeout: 10,
            silence_end_call_timeout: 60,
            mode: 'silence' as const
          },
          asr: {
            quality: 'high' as const,
            provider: 'elevenlabs' as const
          }
        }
      };

      // Log a concise summary of the final tools attached to the agent
      try {
        const configuredTools = (agentConfig as any)?.conversation_config?.agent?.prompt?.tools || [];
        const toolSummary = configuredTools.map((t: any) => {
          if (t?.type === 'webhook') {
            return {
              type: t.type,
              name: t.name,
              url: t.api_schema?.url || t.webhook?.api_schema?.url || undefined,
              timeout_secs: t.response_timeout_secs || undefined
            };
          } else if (t?.type === 'tool_reference') {
            return { type: t.type, tool_id: t.tool_id };
          } else {
            return { type: t?.type, name: t?.name };
          }
        });
        console.log(`[ELEVENLABS_AGENT] Final tools attached (${configuredTools.length}):`, JSON.stringify(toolSummary, null, 2));
      } catch (e) {
        console.warn('[ELEVENLABS_AGENT] Could not summarize tools for logging:', e);
      }



      console.log(`[ELEVENLABS_AGENT] Creating agent with SDK:`, {
        name: agentConfig.name,
        agent_id: agentId,
        voice_id: voiceId,
        prompt_length: prompt.length
      });

      // Log the full agent configuration being sent
      console.log(`[ELEVENLABS_AGENT] Full agent configuration:`, JSON.stringify(agentConfig, null, 2));

      console.log(`[ELEVENLABS_AGENT] Calling client.conversationalAi.createAgent...`);
      const agentResponse = await client.conversationalAi.createAgent(agentConfig);

      // Log the full response from ElevenLabs
      console.log(`[ELEVENLABS_AGENT] Raw agent creation response:`, JSON.stringify(agentResponse, null, 2));
      console.log(`[ELEVENLABS_AGENT] Agent response type:`, typeof agentResponse);
      console.log(`[ELEVENLABS_AGENT] Agent response keys:`, Object.keys(agentResponse || {}));

      if (!agentResponse) {
        console.error(`[ELEVENLABS_AGENT] No response received from ElevenLabs`);
        throw new Error('Agent creation failed - no response from ElevenLabs');
      }

      if (!agentResponse.agent_id) {
        console.error(`[ELEVENLABS_AGENT] No agent_id in response:`, agentResponse);
        throw new Error('Agent creation failed - no agent ID returned');
      }

      console.log(`[ELEVENLABS_AGENT] Agent created successfully: ${agentResponse.agent_id}`);
      console.log(`[ELEVENLABS_AGENT] ✅ NEW AGENT: End Call system tool included in agent ${agentResponse.agent_id}`);

      agentData = {
        agent_id: agentResponse.agent_id,
        name: agentConfig.name,
        created_at: new Date().toISOString()
      };

      console.log(`[ELEVENLABS_AGENT] Created agentData object:`, JSON.stringify(agentData, null, 2));

      // Step 2.5: Validate and fix agent configuration
      console.log(`[ELEVENLABS_AGENT] Validating agent configuration for ${agentData.agent_id}`);
      const configValidation = await validateAndFixUserAgentConfig(agentData.agent_id, apiKey);

      if (configValidation.needsUpdate && configValidation.updatedConfig) {
        console.log(`[ELEVENLABS_AGENT] Applying configuration fixes to new agent`);
        const fixResult = await applyAgentConfigurationFix(
          agentData.agent_id,
          configValidation.updatedConfig,
          apiKey
        );

        if (!fixResult.success) {
          console.warn(`[ELEVENLABS_AGENT] Failed to apply configuration fix: ${fixResult.error}`);
        } else {
          console.log(`[ELEVENLABS_AGENT] Agent configuration fixed successfully`);
        }
      } else {
        console.log('[ELEVENLABS_AGENT] Agent configuration is valid');
      }

      console.log(`[ELEVENLABS_AGENT] Exiting agent creation try block. agentData:`, agentData);

    } catch (sdkError: any) {
      console.error('[ELEVENLABS_AGENT] SDK agent creation failed:', {
        error: sdkError.message,
        stack: sdkError.stack,
        errorName: sdkError.name,
        status: sdkError.status,
        response: sdkError.response,
        agentId,
        agentName: name
      });

      // Provide more specific error information
      let errorMessage = `Failed to create ElevenLabs agent: ${sdkError.message}`;
      if (sdkError.status) {
        errorMessage += ` (Status: ${sdkError.status})`;
      }

      return NextResponse.json({
        error: errorMessage,
        details: sdkError.stack,
        status: sdkError.status
      }, { status: sdkError.status || 500 });
    }

    // Check if agent was created successfully
    console.log(`[ELEVENLABS_AGENT] Checking agentData after try-catch. agentData:`, agentData);
    if (!agentData) {
      console.error(`[ELEVENLABS_AGENT] agentData is null/undefined - agent creation must have failed`);
      return NextResponse.json({
        error: 'Agent creation failed - no agent data available'
      }, { status: 500 });
    }

    console.log(`[ELEVENLABS_AGENT] agentData validation passed. Proceeding with knowledge base setup.`);

    // Step 3: Store agent in database
    let userAgentId: string | null = null;
    try {
      console.log(`[ELEVENLABS_AGENT] Storing agent in database for user: ${userId}`);
      userAgentId = await storeUserAgent({
        userId,
        agentId: agentData.agent_id,
        agentName: agentId, // Store the user-generated ID as the agent name for lookup
        voiceId,
        metadata: {
          prompt,
          documentsUploaded: knowledgeBase.length,
          actualAgentName: properAgentName // Store the actual ElevenLabs agent name in metadata
        }
      });
      console.log(`[ELEVENLABS_AGENT] Agent stored in database with ID: ${userAgentId}`);

      // Verify the agent can be found in database to prevent race conditions
      const { getUserAgentByUserGeneratedId } = await import('../../../../lib/firebase/userAgents');
      const verifyAgent = await getUserAgentByUserGeneratedId(userId, agentId);
      if (verifyAgent) {
        console.log(`[ELEVENLABS_AGENT] Agent successfully verified in database: ${verifyAgent.agentId}`);
      } else {
        console.warn(`[ELEVENLABS_AGENT] Warning: Agent not immediately findable in database after storage`);
      }
    } catch (dbError) {
      console.error('[ELEVENLABS_AGENT] Error storing agent in database:', dbError);
      // Continue anyway - agent was created successfully in ElevenLabs
    }

    // Step 4: Upload knowledge base documents if provided using proper deduplication
    let documentsUploaded = 0;
    if (knowledgeBase.length > 0) {
      try {
        console.log(`[ELEVENLABS_AGENT] Processing ${knowledgeBase.length} documents for knowledge base using deduplication`);

        // Process each document using the proper upload functions
        for (const doc of knowledgeBase) {
          try {
            console.log(`[ELEVENLABS_AGENT] Uploading document: ${doc.title}`);

            // Create a temporary file URL for the document content
            // Since we have text content, we'll create a blob URL
            const blob = new Blob([doc.content], { type: 'text/plain' });
            const tempFileUrl = URL.createObjectURL(blob);

            // Upload document with deduplication
            const uploadResult = await uploadToKnowledgeBaseWithDeduplication(
              tempFileUrl,
              doc.title,
              'text/plain',
              apiKey,
              false // Don't force upload, use deduplication
            );

            console.log(`[ELEVENLABS_AGENT] Upload result for ${doc.title}:`, uploadResult);

            // Associate document with agent (this will handle RAG indexing internally)
            console.log(`[ELEVENLABS_AGENT] Associating document with agent: ${agentData.agent_id}`);
            try {
              const agentUpdateResult = await updateAgentKnowledgeBase(
                agentData.agent_id,
                uploadResult.id,
                apiKey
              );
              console.log(`[ELEVENLABS_AGENT] Agent update result for ${doc.title}:`, agentUpdateResult);
            } catch (agentUpdateError) {
              console.error(`[ELEVENLABS_AGENT] Error updating agent for ${doc.title}:`, agentUpdateError);
              // Continue anyway - document was uploaded
            }

            // Clean up the temporary URL
            URL.revokeObjectURL(tempFileUrl);

            if (uploadResult.id) {
              documentsUploaded++;
              console.log(`[ELEVENLABS_AGENT] Successfully processed document: ${doc.title}`);
            }

          } catch (docError) {
            console.error(`[ELEVENLABS_AGENT] Error processing document ${doc.title}:`, docError);
            // Continue with other documents
          }
        }

        console.log(`[ELEVENLABS_AGENT] Completed document processing: ${documentsUploaded}/${knowledgeBase.length} documents uploaded`);

      } catch (kbError) {
        console.error('[ELEVENLABS_AGENT] Error setting up knowledge base:', kbError);
        // Continue without knowledge base - agent will still work
      }
    }

    // Step 5: Update database with document upload information
    if (userAgentId && documentsUploaded > 0) {
      try {
        await updateUserAgent(userAgentId, {
          metadata: {
            prompt,
            documentsUploaded,
            lastKnowledgeBaseUpdate: new Date()
          }
        });
        console.log(`[ELEVENLABS_AGENT] Updated database with document upload information: ${documentsUploaded} documents`);
      } catch (updateError) {
        console.error('[ELEVENLABS_AGENT] Error updating database with document info:', updateError);
      }
    }

    // Step 6: Create a conversation session using SDK
    let conversationId = null;
    // TODO: Fix conversation creation - the SDK method name needs to be determined
    console.log(`[ELEVENLABS_AGENT] Skipping conversation creation for now - will be handled by useConversation hook`);
    console.log(`[ELEVENLABS_AGENT] Agent created successfully and ready for voice conversations`);

    const finalResponse = {
      success: true,
      agentId: agentData.agent_id,
      conversationId,
      name: properAgentName, // Return the proper PMO agent name
      voiceId,
      documentsUploaded,
      message: `PMO agent created successfully with ${documentsUploaded} documents uploaded and End Call tool and ready for voice conversations`,
      wasExisting: false,
      userAgentId,
      configurationIssues: [],
      configurationFixed: false,
      hasEndCallTool: true
    };

    console.log(`[ELEVENLABS_AGENT] Sending final success response:`, JSON.stringify(finalResponse, null, 2));
    return NextResponse.json(finalResponse);

  } catch (error: any) {
    console.error('[ELEVENLABS_AGENT] Error creating agent:', error);
    return NextResponse.json({
      success: false,
      error: error.message || 'Failed to create ElevenLabs agent'
    }, { status: 500 });
  }
}

/**
 * GET endpoint to check if an agent exists or list all agents
 * Query parameters:
 * - agentId: Check if specific agent exists
 * - list: Set to 'true' to list all agents
 * - search: Search agents by name pattern
 */
export async function GET(request: NextRequest) {
  try {
    // Check authentication
    const session = await getServerSession(authOptions);
    if (!session?.user?.email) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const { searchParams } = new URL(request.url);
    const agentId = searchParams.get('agentId');
    const listAgents = searchParams.get('list') === 'true';
    const searchQuery = searchParams.get('search');

    // Get ElevenLabs API key
    const apiKey = process.env.NEXT_PUBLIC_ELEVENLABS_COMPANY_API_KEY;
    if (!apiKey) {
      return NextResponse.json({
        error: 'ElevenLabs API key not configured'
      }, { status: 500 });
    }

    const client = new ElevenLabsClient({ apiKey });

    // Handle agent listing
    if (listAgents) {
      try {
        console.log(`[ELEVENLABS_AGENT] Listing all agents${searchQuery ? ` with search: ${searchQuery}` : ''}`);

        // Use direct API call since SDK method structure is unclear
        const url = new URL('https://api.elevenlabs.io/v1/convai/agents');
        if (searchQuery) {
          url.searchParams.append('search', searchQuery);
        }
        url.searchParams.append('page_size', '100');

        const response = await fetch(url.toString(), {
          method: 'GET',
          headers: {
            'xi-api-key': apiKey,
            'Content-Type': 'application/json',
          },
        });

        if (!response.ok) {
          const errorText = await response.text();
          throw new Error(`Failed to list agents: ${response.status} ${response.statusText} - ${errorText}`);
        }

        const agentsResponse = await response.json();
        console.log(`[ELEVENLABS_AGENT] Found ${agentsResponse.agents?.length || 0} agents`);

        return NextResponse.json({
          success: true,
          agents: agentsResponse.agents || [],
          has_more: agentsResponse.has_more || false,
          next_cursor: agentsResponse.next_cursor || null
        });
      } catch (listError: any) {
        console.error('[ELEVENLABS_AGENT] Error listing agents:', listError);
        return NextResponse.json({
          error: 'Failed to list agents',
          details: listError.message
        }, { status: 500 });
      }
    }

    // Handle single agent existence check
    if (!agentId) {
      return NextResponse.json({
        error: 'agentId parameter is required when not listing agents'
      }, { status: 400 });
    }

    // Check if agent exists using SDK
    try {
      const agentData = await client.conversationalAi.getAgent(agentId);

      if (agentData && agentData.agent_id === agentId) {
        return NextResponse.json({
          exists: true,
          agent: agentData
        });
      } else {
        return NextResponse.json({
          exists: false
        });
      }
    } catch (fetchError: any) {
      // 404 means agent doesn't exist - this is expected for new agents
      if (fetchError.message && (fetchError.message.includes('404') || fetchError.message.includes('not found'))) {
        return NextResponse.json({
          exists: false
        });
      } else {
        return NextResponse.json({
          error: 'Failed to check agent status',
          details: fetchError.message
        }, { status: 500 });
      }
    }

  } catch (error: any) {
    console.error('[ELEVENLABS_AGENT] Error in GET endpoint:', error);
    return NextResponse.json({
      success: false,
      error: error.message || 'Failed to process request'
    }, { status: 500 });
  }
}


