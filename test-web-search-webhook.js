/**
 * Test script for the web search webhook endpoint
 * Run with: node test-web-search-webhook.js
 */

const fetch = require('node-fetch');

async function testWebSearchWebhook() {
  const baseUrl = process.env.NEXTAUTH_URL || 'http://localhost:3000';
  const webhookSecret = process.env.ELEVENLABS_WEBHOOK_SECRET || 'default-secret';

  console.log('🧪 Testing Web Search Webhook Endpoint');
  console.log(`📍 Base URL: ${baseUrl}`);
  console.log(`🔐 Using webhook secret: ${webhookSecret.substring(0, 10)}...`);

  // Test 1: Valid search request
  try {
    console.log('\n📋 Test 1: Valid search request');
    
    const testPayload = {
      query: 'JavaScript best practices 2024',
      context: 'Creating documentation for development team',
      searchPurpose: 'best_practices'
    };

    const response = await fetch(`${baseUrl}/api/elevenlabs/web-search-webhook`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${webhookSecret}`
      },
      body: JSON.stringify(testPayload)
    });

    const responseText = await response.text();
    console.log(`📊 Status: ${response.status} ${response.statusText}`);
    
    if (response.ok) {
      const result = JSON.parse(responseText);
      console.log('✅ Success!');
      console.log(`📝 Message length: ${result.message?.length || 0} characters`);
      console.log(`🔍 Results count: ${result.results?.length || 0}`);
      console.log(`📊 Search details:`, result.searchDetails);
    } else {
      console.log('❌ Failed!');
      console.log('📄 Response:', responseText);
    }
  } catch (error) {
    console.error('❌ Test 1 failed:', error.message);
  }

  // Test 2: Missing query parameter
  try {
    console.log('\n📋 Test 2: Missing query parameter');
    
    const testPayload = {
      context: 'Testing error handling',
      searchPurpose: 'general'
    };

    const response = await fetch(`${baseUrl}/api/elevenlabs/web-search-webhook`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${webhookSecret}`
      },
      body: JSON.stringify(testPayload)
    });

    const responseText = await response.text();
    console.log(`📊 Status: ${response.status} ${response.statusText}`);
    
    if (response.status === 400) {
      console.log('✅ Correctly rejected missing query!');
      console.log('📄 Response:', responseText);
    } else {
      console.log('❌ Should have returned 400 status');
      console.log('📄 Response:', responseText);
    }
  } catch (error) {
    console.error('❌ Test 2 failed:', error.message);
  }

  // Test 3: Invalid authentication
  try {
    console.log('\n📋 Test 3: Invalid authentication');
    
    const testPayload = {
      query: 'test query',
      searchPurpose: 'general'
    };

    const response = await fetch(`${baseUrl}/api/elevenlabs/web-search-webhook`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': 'Bearer invalid-secret'
      },
      body: JSON.stringify(testPayload)
    });

    const responseText = await response.text();
    console.log(`📊 Status: ${response.status} ${response.statusText}`);
    
    if (response.status === 401) {
      console.log('✅ Correctly rejected invalid auth!');
      console.log('📄 Response:', responseText);
    } else {
      console.log('❌ Should have returned 401 status');
      console.log('📄 Response:', responseText);
    }
  } catch (error) {
    console.error('❌ Test 3 failed:', error.message);
  }

  // Test 4: OPTIONS request (CORS)
  try {
    console.log('\n📋 Test 4: OPTIONS request (CORS)');
    
    const response = await fetch(`${baseUrl}/api/elevenlabs/web-search-webhook`, {
      method: 'OPTIONS'
    });

    console.log(`📊 Status: ${response.status} ${response.statusText}`);
    console.log('🔧 CORS Headers:');
    console.log(`   Access-Control-Allow-Origin: ${response.headers.get('Access-Control-Allow-Origin')}`);
    console.log(`   Access-Control-Allow-Methods: ${response.headers.get('Access-Control-Allow-Methods')}`);
    console.log(`   Access-Control-Allow-Headers: ${response.headers.get('Access-Control-Allow-Headers')}`);
    
    if (response.status === 200) {
      console.log('✅ CORS preflight handled correctly!');
    } else {
      console.log('❌ CORS preflight failed');
    }
  } catch (error) {
    console.error('❌ Test 4 failed:', error.message);
  }

  console.log('\n🏁 Web Search Webhook Tests Complete');
}

// Run the tests
testWebSearchWebhook().catch(console.error);
