import { NextRequest, NextResponse } from 'next/server';
import { createDocumentationAgent } from '../../../lib/agents/documentation/createDocumentationAgent';
import type { CreateDocumentationAgentOptions } from '../../../lib/agents/documentation/createDocumentationAgent';
import { adminDb } from '../../../components/firebase/admin';
import { v4 as uuidv4 } from 'uuid';
import { Timestamp } from 'firebase-admin/firestore';

// Helper function to remove undefined values from objects (Firestore doesn't allow undefined)
// Correctly preserves Date and Firestore Timestamp objects
function removeUndefinedValues(obj: any): any {
  if (obj === null || obj === undefined) {
    return undefined; // use undefined to allow parent to drop the field
  }

  // Preserve Date and Firestore Timestamp values as-is
  if (obj instanceof Date || obj instanceof Timestamp) {
    return obj;
  }

  if (Array.isArray(obj)) {
    return obj.map(removeUndefinedValues).filter(item => item !== undefined);
  }

  if (typeof obj === 'object') {
    const cleaned: any = {};
    for (const [key, value] of Object.entries(obj)) {
      const cleanedValue = removeUndefinedValues(value);
      if (cleanedValue !== undefined) {
        cleaned[key] = cleanedValue;
      }
    }
    return cleaned;
  }

  return obj;
}

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();

    // Validate required fields
    if (!body.userId || !body.category || !body.query) {
      return NextResponse.json(
        { error: 'Missing required fields: userId, category, and query are required' },
        { status: 400 }
      );
    }

    // Prepare options for createDocumentationAgent
    const options: CreateDocumentationAgentOptions = {
      userId: body.userId,
      category: body.category,
      query: body.query,
      namespaces: body.namespaces,
      strategy: body.strategy || 'standalone',
      topK: body.topK || 5,
      modelProvider: body.modelProvider || 'google',
      modelName: body.modelName || 'gemini-2.5-pro',
      filters: body.filters || {},
      abTest: body.abTest || false,
      abStrategies: body.abStrategies,
      generatePDF: body.generatePDF !== false, // Default to true
      customTitle: body.customTitle,
      // IMPORTANT: Default to false (keep full content) unless explicitly set to true
      removeTechnicalMetadata: body.removeTechnicalMetadata ?? false,
      // PMO integration options
      createPMORecord: body.createPMORecord !== false, // Default to true for automatic PMO tracking
      pmoRecordTitle: body.pmoRecordTitle || body.customTitle
    };

    // Call createDocumentationAgent
    const result = await createDocumentationAgent.createDocumentation(options);

    if (!result.success) {
      return NextResponse.json(
        { error: result.error || 'Documentation agent failed' },
        { status: 500 }
      );
    }

    // Store the documentation output to Firebase global Agent_Output collection
    // Following the same pattern as Marketing, Research, and Business Analysis agents
    let requestId: string | null = null;
    try {
      requestId = uuidv4();
      const serverProcessingTimestamp = new Date();

      // Prepare data to be stored in global Firestore collection (matching other agent workflows)
      const agentOutputData = {
        requestId,
        timestamp: serverProcessingTimestamp,
        createdAt: serverProcessingTimestamp,
        agentType: 'Documentation Generation', // Agent type for PMO filtering
        userId: body.userId,
        prompt: body.query,
        result: {
          thinking: `Documentation generation using ${result.strategy} strategy`,
          output: result.markdownContent,
          documentUrl: result.pdfResult?.downloadUrl || null
        },
        agentMessages: [], // Documentation Generation doesn't use multi-agent collaboration
        modelInfo: {
          provider: body.modelProvider || 'google',
          model: body.modelName || 'gemini-2.5-pro'
        },
        metadata: {
          category: body.category,
          strategy: result.strategy,
          generatedTitle: result.generatedTitle,
          queryDurationMs: result.metrics?.queryDurationMs,
          totalMatches: result.metrics?.totalMatches,
          namespacesQueried: result.metrics?.namespacesQueried,
          pdfGenerated: !!result.pdfResult,
          pmoRecordCreated: !!result.pmoRecord,
          agentExecutionTimeMs: result.agentMetadata?.totalExecutionTimeMs,
          toolExecutionTimeMs: result.agentMetadata?.toolExecutionTimeMs
        }
      };

      // Clean the data to remove undefined values (Firestore doesn't allow undefined)
      const cleanedData = removeUndefinedValues(agentOutputData);

      console.log(`[DOCUMENTATION_AGENT_OUTPUT] Storing documentation generation output with requestId: ${requestId}`);
      console.log(`[DOCUMENTATION_AGENT_OUTPUT] Data being stored:`, {
        requestId: cleanedData.requestId,
        agentType: cleanedData.agentType,
        userId: cleanedData.userId,
        timestamp: cleanedData.timestamp,
        createdAt: cleanedData.createdAt,
        hasResult: !!cleanedData.result,
        hasMetadata: !!cleanedData.metadata
      });

      await adminDb.collection('Agent_Output').doc(requestId).set(cleanedData);
      console.log(`[DOCUMENTATION_AGENT_OUTPUT] Successfully stored documentation generation output with requestId: ${requestId}`);

      // Add the requestId to the result for reference
      (result as any).requestId = requestId;
    } catch (storageError) {
      console.error(`[DOCUMENTATION_AGENT_OUTPUT] Error storing documentation generation output:`, storageError);
      // Continue with the response even if storage fails
    }

    // Return the result with agent metadata and PMO record information
    return NextResponse.json({
      success: true,
      data: result,
      requestId, // Include requestId for tracking
      agentMetadata: result.agentMetadata, // Include agent execution metadata
      pmoRecord: result.pmoRecord // Include PMO record information if created
    });

  } catch (error: any) {
    console.error('Create documentation API error:', error);
    return NextResponse.json(
      { error: error?.message || 'Internal server error' },
      { status: 500 }
    );
  }
}
