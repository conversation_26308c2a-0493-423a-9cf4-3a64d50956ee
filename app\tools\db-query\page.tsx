'use client';
import { useEffect, useMemo, useRef, useState } from 'react';
import { Database, Search, Play, Beaker, Timer, BarChart3, ChevronDown } from 'lucide-react';
import MarkdownRenderer from '../../../components/MarkdownRenderer';
import { collection, getDocs } from 'firebase/firestore';
import { db } from '../../lib/firebase/config';


// Local, minimal formatter to convert the structured DB Query result to Markdown
function formatDbQueryToMarkdown(payload: any, defaultModel = 'gemini-2.5-pro'): string {
  try {
    if (!payload) return 'No data.';
    const mode = payload.mode || 'single';

    if (mode === 'single' && payload.run && payload.run.response) {
      const run = payload.run;
      const resp = run.response || {};
      const stats = resp.stats || {};
      const lines: string[] = [];
      lines.push('# DB Query Result');
      lines.push('');
      lines.push(`- Mode: single`);
      if (run.strategyId) lines.push(`- Strategy: ${run.strategyId}`);
      if (stats.namespacesQueried?.length) lines.push(`- Namespaces: ${stats.namespacesQueried.join(', ')}`);
      if (typeof stats.totalMatches === 'number') lines.push(`- Total matches: ${stats.totalMatches}`);
      if (typeof stats.timingsMs?.total === 'number') lines.push(`- Total time: ${stats.timingsMs.total} ms`);
      if (resp.llmAnalysis) lines.push(`- Provider/Model: ${(resp.llmProvider || 'unknown') + ' / ' + (resp.llmModel || defaultModel)}`);

      if (resp.llmAnalysis) {
        lines.push('');
        lines.push('## Answer');
        lines.push('');
        lines.push(resp.llmAnalysis);
      }

      const results = Array.isArray(resp.results) ? resp.results.slice(0, 10) : [];
      if (results.length) {
        lines.push('');
        lines.push('## Top Matches');
        lines.push('');
        for (const r of results) {
          const path = r?.firestoreMetadata?.filePath || r?.pineconeMetadata?.filePath || r?.chunkId || '';
          const score = typeof r?.score === 'number' ? r.score.toFixed(3) : (r?.score ?? '');
          const snippet = (r?.content || '').slice(0, 280).replace(/\s+/g, ' ').trim();
          lines.push(`- ${path} — score ${score}`);
          if (snippet) {
            lines.push('');
            lines.push(`  ${snippet}`);
          }
        }
      }

      return lines.join('\n').trim() + '\n';
    }

    if (mode === 'ab' && payload.result && Array.isArray(payload.result.runs)) {
      const res = payload.result;
      const lines: string[] = [];
      lines.push('# DB Query A/B Comparison');
      lines.push('');
      lines.push(`- Query: ${res.query || ''}`);
      lines.push(`- Category: ${res.category || ''}`);
      lines.push(`- Strategies: ${res.runs.map((r: any) => r.strategyId).join(', ')}`);

      res.runs.forEach((run: any, idx: number) => {
        const r = run.response || {};
        const stats = r.stats || {};
        lines.push('');
        lines.push(`## ${idx + 1}. Strategy: ${run.strategyId}`);
        if (typeof run.metrics?.durationMs === 'number') lines.push(`- Duration: ${run.metrics.durationMs} ms`);
        if (typeof run.quality?.avgScore === 'number') lines.push(`- Avg score: ${run.quality.avgScore.toFixed(3)}`);
        if (typeof run.quality?.diversity === 'number') lines.push(`- Diversity: ${run.quality.diversity}`);
        if (typeof run.quality?.coverage === 'number') lines.push(`- Coverage: ${run.quality.coverage}`);
        if (typeof stats.totalMatches === 'number') lines.push(`- Matches: ${stats.totalMatches}`);
        if (stats.namespacesQueried?.length) lines.push(`- Namespaces: ${stats.namespacesQueried.join(', ')}`);
        if (r.llmAnalysis) lines.push(`- Provider/Model: ${(r.llmProvider || 'unknown') + ' / ' + (r.llmModel || defaultModel)}`);

        if (r.llmAnalysis) {
          lines.push('');
          lines.push('### Model Answer');
          lines.push('');
          lines.push(r.llmAnalysis);
        }

        const top = Array.isArray(r.results) ? r.results.slice(0, 5) : [];
        if (top.length) {
          lines.push('');
          lines.push('### Top Matches');
          lines.push('');
          for (const hit of top) {
            const path = hit?.firestoreMetadata?.filePath || hit?.pineconeMetadata?.filePath || hit?.chunkId || '';
            const score = typeof hit?.score === 'number' ? hit.score.toFixed(3) : (hit?.score ?? '');
            const snippet = (hit?.content || '').slice(0, 200).replace(/\s+/g, ' ').trim();
            lines.push(`- ${path} — score ${score}`);
            if (snippet) {
              lines.push('');
              lines.push(`  ${snippet}`);
            }
          }
        }
      });

      return lines.join('\n').trim() + '\n';
    }

    return '```json\n' + JSON.stringify(payload, null, 2) + '\n```\n';
  } catch {
    return 'Failed to format result.';
  }
}
  function CopyMarkdownButton({ getContent }: { getContent: () => string }) {
    const [copied, setCopied] = useState(false);
    const onCopy = async () => {
      try {
        await navigator.clipboard.writeText(getContent());
        setCopied(true);
        setTimeout(() => setCopied(false), 1200);
      } catch {}
    };
    return (
      <button onClick={onCopy} className="px-2 py-1 text-xs bg-zinc-800 hover:bg-zinc-700 rounded border border-zinc-600">
        {copied ? 'Copied!' : 'Copy Markdown'}
      </button>
    );
  }

  function SavePdfButton({ getContent, title, userId, selectedProvider, selectedModel, abMode, strategyId, namespaces, query, filtersJson, category }: { getContent: () => string; title: string; userId: string; selectedProvider: string; selectedModel: string; abMode: boolean; strategyId: string; namespaces: string; query: string; filtersJson: string; category: string; }) {
    const [saving, setSaving] = useState(false);
    const onSave = async () => {
      try {
        setSaving(true);
        const content = getContent();
        const pmoId = (globalThis as any).crypto?.randomUUID ? (globalThis as any).crypto.randomUUID() : Math.random().toString(36).slice(2);
        const filtersMeta = (() => { try { return JSON.parse(filtersJson || '{}'); } catch { return {}; } })();
        // Generate a concise title from the query using Groq (fallback to provided title)
        let finalTitle = title;
        try {
          if (query && query.trim()) {
            console.log('SavePdfButton: Generating title for query:', query);
            const tRes = await fetch('/api/generate-title', {
              method: 'POST',
              headers: { 'Content-Type': 'application/json' },
              body: JSON.stringify({ query, category })
            });
            if (tRes.ok) {
              const tData = await tRes.json();
              console.log('SavePdfButton: Title generation response:', tData);
              if (tData?.title) {
                finalTitle = tData.title;
                console.log('SavePdfButton: Using generated title:', finalTitle);
              }
            } else {
              console.log('SavePdfButton: Title generation failed with status:', tRes.status);
            }
          }
        } catch (e) {
          console.log('SavePdfButton: Title generation error:', e);
        }

        const res = await fetch('/api/pmo-process-document', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({
            title: finalTitle,
            content,
            pmoId,
            userId,
            category: category, // Keep the user-selected category unchanged
            metadata: {
              source: 'DB Query Tool',
              provider: selectedProvider,
              model: selectedModel,
              strategy: abMode ? 'ab' : strategyId,
              namespaces: namespaces,
              query,
              filters: JSON.stringify(filtersMeta),
              generatedAt: new Date().toISOString(),
              isPMOGenerated: true // Flag to identify PMO-generated documents
            }
          })
        });
        const data = await res.json().catch(() => ({}));
        if (!res.ok || !data?.success) {
          throw new Error(data?.error || 'Failed to save PDF');
        }
        if (data.downloadUrl) {
          // Open the PDF in a new tab
          window.open(data.downloadUrl, '_blank');

          // Redirect to PMO Documents tab to view the document in the list
          setTimeout(() => {
            window.open('/services/pmo?tab=documents', '_blank');
          }, 1000); // Small delay to ensure PDF opens first
        }
      } catch (e: any) {
        alert(e?.message || 'Failed to create PDF');
      } finally {
        setSaving(false);
      }
    };
    return (
      <div className="flex items-center gap-2">
        <button onClick={onSave} disabled={saving} className="px-2 py-1 text-xs bg-emerald-700 hover:bg-emerald-600 rounded border border-emerald-500">
          {saving ? 'Saving…' : 'Save as PDF'}
        </button>
        <button
          onClick={() => window.open('/services/pmo?tab=documents', '_blank')}
          className="px-2 py-1 text-xs bg-zinc-800 hover:bg-zinc-700 rounded border border-zinc-600"
          title="Open PMO Documents in a new tab"
        >
          View in PMO
        </button>
      </div>
    );
  }




interface RunState {
  loading: boolean;
  error?: string;
  data?: any;
}

const LS_KEY = 'dbQueryTool:lastState';

export default function DBQueryToolAppPage() {
  const [strategies, setStrategies] = useState<string[]>([]);
  const [userId, setUserId] = useState<string>('<EMAIL>');
  const [category, setCategory] = useState<string>('Codebase');
  const [query, setQuery] = useState<string>('Summarize the selected codebase files and their purpose?');
  // Model provider & model selection (optional)
  type ModelProvider = 'openai' | 'anthropic' | 'groq' | 'google';
  const providerModels: Record<ModelProvider, string[]> = {
    openai: ['gpt-5-2025-08-07', 'gpt-4o', 'gpt-4.1-2025-04-14', 'o3-2025-04-16', 'o3-mini-2025-01-31', 'o1-mini-2024-09-12'],
    anthropic: ['claude-sonnet-4-0', 'claude-opus-4-0'],
    groq: ['llama-3.3-70b-versatile', 'meta-llama/llama-4-maverick-17b-128e-instruct', 'deepseek-r1-distill-llama-70b', 'openai/gpt-oss-20b', 'openai/gpt-oss-120b'],
    google: ['gemini-2.5-pro', 'gemini-2.5-flash', 'gemini-1.5-pro', 'gemini-1.5-flash']
  };
  const [selectedProvider, setSelectedProvider] = useState<ModelProvider>('google');
  const [selectedModel, setSelectedModel] = useState<string>('gemini-2.5-pro');
  // Dropdown open/close control similar to PMO pattern
  const catDropdownRef = useRef<HTMLDivElement | null>(null);
  const [isCatDropdownOpen, setIsCatDropdownOpen] = useState(false);

  useEffect(() => {
    function handleClickOutside(event: MouseEvent) {
      if (catDropdownRef.current && !catDropdownRef.current.contains(event.target as Node)) {
        setIsCatDropdownOpen(false);
      }
    }
    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, []);

	  // Ensure model always exists for selected provider
	  useEffect(() => {
	    if (!providerModels[selectedProvider].includes(selectedModel)) {
	      setSelectedModel(providerModels[selectedProvider][0]);
	    }
	  }, [selectedProvider]);


  // Map of category -> file count and loading/error for categories
  const [categoryCounts, setCategoryCounts] = useState<Record<string, number>>({});
  const [catsLoading, setCatsLoading] = useState<boolean>(false);
  const [catsError, setCatsError] = useState<string | null>(null);
  useEffect(() => {
    let cancelled = false;
    async function loadCounts() {
      try {
        if (!userId) { if (!cancelled) { setCategoryCounts({}); setCatsLoading(false); } return; }
        if (!cancelled) { setCatsLoading(true); setCatsError(null); }
        const filesRef = collection(db, 'users', userId, 'files');
        const snap = await getDocs(filesRef);
        const counts: Record<string, number> = {};
        snap.forEach(doc => {
          const data: any = doc.data();
          const cat = data?.category || 'Uncategorized';
          counts[cat] = (counts[cat] || 0) + 1;
        });
        if (!cancelled) setCategoryCounts(counts);
      } catch (err: any) {
        if (!cancelled) { setCategoryCounts({}); setCatsError(err?.message || 'Failed to load categories'); }
      } finally {
        if (!cancelled) setCatsLoading(false);
      }
    }
    loadCounts();
    return () => { cancelled = true; };
  }, [userId]);

  const [namespaces, setNamespaces] = useState<string>('');
  const [strategyId, setStrategyId] = useState<string>('standalone');
  const [abMode, setAbMode] = useState<boolean>(false);
  const [topK, setTopK] = useState<number>(5);
  // LLM analysis is now mandatory for all strategies
  const [filtersJson, setFiltersJson] = useState<string>('{}');

  // Build category options from the fetched counts, filter Unknown/Uncategorized, and sort A-Z
  const categoryOptions = useMemo(() => {
    const keys = Object.keys(categoryCounts || {});
    return keys.filter(k => k && k !== 'Unknown' && k !== 'Uncategorized').sort((a, b) => a.localeCompare(b));
  }, [categoryCounts]);

  const [run, setRun] = useState<RunState>({ loading: false });

  // Load last-used inputs
  useEffect(() => {
    try {
      const raw = localStorage.getItem(LS_KEY);
      if (!raw) return;
      const s = JSON.parse(raw);
      if (s.userId) setUserId(s.userId);
      if (s.category) setCategory(s.category);
      if (s.query) setQuery(s.query);
      if (typeof s.namespaces === 'string') setNamespaces(s.namespaces);
      if (typeof s.strategyId === 'string') setStrategyId(s.strategyId);
      if (typeof s.abMode === 'boolean') setAbMode(s.abMode);
      if (typeof s.topK === 'number') setTopK(s.topK);
      if (typeof s.selectedProvider === 'string') setSelectedProvider(s.selectedProvider);
      if (typeof s.selectedModel === 'string') setSelectedModel(s.selectedModel);
      if (typeof s.filtersJson === 'string') setFiltersJson(s.filtersJson);
    } catch {}
  }, []);

  // Persist inputs
  useEffect(() => {
    const state = { userId, category, query, namespaces, strategyId, abMode, topK, filtersJson, selectedProvider, selectedModel };
    try { localStorage.setItem(LS_KEY, JSON.stringify(state)); } catch {}
  }, [userId, category, query, namespaces, strategyId, abMode, topK, filtersJson, selectedProvider, selectedModel]);

  useEffect(() => {
    fetch('/api/db-query').then(r => r.json()).then(j => setStrategies(j.strategies || [])).catch(() => {});
  }, []);

  const abStrategies = useMemo(() => strategies, [strategies]);

  const onRun = async () => {
    setRun({ loading: true });
    try {
      // Prepare request body for the create-documentation API
      const requestBody = {
        userId,
        category,
        query,
        namespaces: namespaces.trim() ? namespaces.split(',').map(s => s.trim()) : undefined,
        strategy: abMode ? 'standalone' : strategyId,
        topK,
        modelProvider: selectedProvider,
        modelName: selectedModel,
        filters: JSON.parse(filtersJson || '{}'),
        abTest: abMode,
        abStrategies: abMode ? abStrategies : undefined,
        generatePDF: true, // Enable PDF generation by default
        removeTechnicalMetadata: false // Keep all metadata for the UI display
      };

      // Call the create-documentation API endpoint
      const res = await fetch('/api/create-documentation', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(requestBody)
      });

      const responseData = await res.json();

      if (!res.ok || !responseData.success) {
        throw new Error(responseData.error || 'Documentation tool failed');
      }

      const result = responseData.data;

      // Transform the result to match the expected format for the existing UI
      const transformedData = {
        mode: abMode ? 'ab' : 'single',
        run: abMode ? undefined : {
          strategyId: result.strategy,
          response: {
            llmAnalysis: result.markdownContent,
            llmProvider: selectedProvider,
            llmModel: selectedModel,
            results: (result.rawData as any)?.response?.results || [],
            stats: result.metrics ? {
              totalMatches: result.metrics.totalMatches,
              namespacesQueried: result.metrics.namespacesQueried,
              timingsMs: { total: result.metrics.queryDurationMs }
            } : {}
          }
        },
        result: abMode ? result.rawData : undefined
      };

      setRun({ loading: false, data: transformedData });
    } catch (e: any) {
      setRun({ loading: false, error: e.message });
    }
  };

  const Badge = ({ label, value, color = 'bg-zinc-800 text-zinc-200' }: { label: string; value: string | number; color?: string }) => (
    <span className={`inline-flex items-center gap-1 px-2 py-1 rounded-md text-xs ${color}`}>
      <BarChart3 size={12} /> <span className="text-zinc-400">{label}:</span> <span className="font-semibold text-white">{value}</span>
    </span>
  );

  const ProviderModelBadge = ({ provider, model }: { provider?: string; model?: string }) => (
    <span className="inline-flex items-center gap-1 px-2 py-1 rounded-md text-xs bg-zinc-800 text-zinc-200">
      <BarChart3 size={12} />
      <span className="text-zinc-400">Provider/Model:</span>
      <span className="font-semibold text-white">{(provider || selectedProvider) + ' / ' + (model || selectedModel)}</span>
    </span>
  );
  // Small helper to render formatted markdown from the JSON payload
  function MarkdownContent({ payload, defaultModel = 'gemini-2.5-pro' }: { payload: any; defaultModel?: string }) {
    const [md, setMd] = useState<string>('');
    useEffect(() => {
      try { setMd(formatDbQueryToMarkdown(payload, defaultModel)); }
      catch { setMd('Failed to format result.'); }
    }, [payload, defaultModel]);
    return <MarkdownRenderer content={md} />;
  }


  const abResult = run.data?.mode === 'ab' ? run.data.result : null;

  return (
    <main className="min-h-screen bg-zinc-950 text-zinc-100 p-4 md:p-6">
      <div className="max-w-6xl mx-auto space-y-4">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-3">
            <Database className="text-blue-400" size={28} />
            <div>
              <h1 className="text-2xl font-bold text-white">DB Query Tool</h1>
              <p className="text-sm text-zinc-400">Run unified codebase queries with switchable strategies or A/B testing.</p>
            </div>
          </div>
        </div>

        {/* Form card */}
        <div className="bg-zinc-900 border border-zinc-700 rounded-md p-4 space-y-4">
          <div className="grid gap-4 md:grid-cols-2">
            <div>
              <label className="text-xs uppercase text-zinc-400">User ID</label>
              <input value={userId} onChange={e => setUserId(e.target.value)} className="mt-1 w-full px-3 py-2 bg-zinc-800 border border-zinc-700 rounded-md" />
            </div>
            <div className="relative" ref={catDropdownRef}>
              <label className="block text-xs uppercase text-zinc-400 mb-1">Category</label>
              <button
                type="button"
                className="flex items-center justify-between w-full px-3 py-2 bg-zinc-800 border border-zinc-700 rounded-md cursor-pointer text-left"
                onClick={() => setIsCatDropdownOpen(!isCatDropdownOpen)}
                aria-haspopup="listbox"
                aria-expanded={isCatDropdownOpen}
                title={category || 'Select a category'}
              >
                <span className={`truncate ${category ? 'text-white' : 'text-zinc-400'}`}>
                  {category || (catsLoading ? 'Loading…' : catsError ? 'Error loading categories' : 'Select a category')}
                </span>
                <ChevronDown className={`w-4 h-4 text-zinc-400 transition-transform flex-shrink-0 ${isCatDropdownOpen ? 'rotate-180' : ''}`} />
              </button>

              {isCatDropdownOpen && (
                <div className="absolute z-20 w-full mt-1 bg-zinc-900 border border-zinc-700 rounded-md shadow-lg max-h-60 overflow-auto">
                  <div className="py-1">
                    <ul role="listbox" className="py-1">
                      {catsLoading ? (
                        <li className="px-4 py-3 text-zinc-400 text-center text-sm">Loading…</li>
                      ) : catsError ? (
                        <li className="px-4 py-3 text-red-400 text-center text-sm">Error loading categories</li>
                      ) : categoryOptions.length > 0 ? (
                        categoryOptions.map((c) => (
                          <li
                            key={c}
                            role="option"
                            aria-selected={category === c}
                            className="px-4 py-2 hover:bg-amber-900/30 cursor-pointer text-sm"
                            onClick={() => { setCategory(c); setIsCatDropdownOpen(false); }}
                            title={c}
                          >
                            <div className="flex items-center justify-between">
                              <div className="font-medium truncate" title={c}>{c}</div>
                              <div className="text-xs text-zinc-400 ml-2">{categoryCounts[c] ? `${categoryCounts[c]} file(s)` : ''}</div>
                            </div>
                          </li>
                        ))
                      ) : (
                        <li className="px-4 py-3 text-zinc-400 text-center text-sm">No categories available</li>
                      )}
                    </ul>
                  </div>
                </div>
              )}
            </div>
            <div className="md:col-span-2">
              <label className="text-xs uppercase text-zinc-400">Query</label>
              <div className="mt-1 flex items-center gap-2">
                <Search size={16} className="text-zinc-500" />
                <input value={query} onChange={e => setQuery(e.target.value)} className="flex-1 px-3 py-2 bg-zinc-800 border border-zinc-700 rounded-md" />
              </div>
            </div>
            <div className="md:col-span-2">
              <label className="text-xs uppercase text-zinc-400">Namespaces (comma-separated)</label>
              <input value={namespaces} onChange={e => setNamespaces(e.target.value)} className="mt-1 w-full px-3 py-2 bg-zinc-800 border border-zinc-700 rounded-md" />
            </div>
          </div>

          <div className="flex flex-wrap items-center gap-4 pt-2">
            <label className="flex items-center gap-2 text-sm">
              <input type="checkbox" checked={abMode} onChange={e => setAbMode(e.target.checked)} />
              A/B Test
            </label>
            {!abMode && (
              <label className="text-sm flex items-center gap-2">
                Strategy
                <select value={strategyId} onChange={e => setStrategyId(e.target.value)} className="px-2 py-2 bg-zinc-800 border border-zinc-700 rounded-md">
                  {strategies.map(s => <option key={s} value={s}>{s}</option>)}
                </select>
              </label>
            )}

            <label className="text-sm flex items-center gap-2">
              Provider
              <select value={selectedProvider} onChange={e => setSelectedProvider(e.target.value as ModelProvider)} className="px-2 py-2 bg-zinc-800 border border-zinc-700 rounded-md">
                {(Object.keys(providerModels) as ModelProvider[]).map((prov) => (
                  <option key={prov} value={prov}>{prov.charAt(0).toUpperCase() + prov.slice(1)}</option>
                ))}
              </select>
            </label>

            <label className="text-sm flex items-center gap-2">
              Model
              <select value={selectedModel} onChange={e => setSelectedModel(e.target.value)} className="px-2 py-2 bg-zinc-800 border border-zinc-700 rounded-md max-w-[280px]">
                {providerModels[selectedProvider].map((m) => (
                  <option key={m} value={m}>{m}</option>
                ))}
              </select>
            </label>

            <label className="text-sm flex items-center gap-2">
              topK per namespace
              <input type="number" value={topK} onChange={e => setTopK(parseInt(e.target.value || '5', 10))} className="w-24 px-2 py-2 bg-zinc-800 border border-zinc-700 rounded-md" />
            </label>
          </div>

          <div>
            <label className="text-xs uppercase text-zinc-400">Filters (JSON)</label>
            <textarea rows={6} value={filtersJson} onChange={e => setFiltersJson(e.target.value)} className="mt-1 w-full px-3 py-2 bg-zinc-950 border border-zinc-800 rounded-md font-mono text-sm" />
          </div>

          <div className="flex items-center gap-2">
            <button onClick={onRun} disabled={run.loading} className={`flex items-center gap-2 px-3 py-2 rounded-md ${run.loading ? 'bg-zinc-700' : 'bg-blue-600 hover:bg-blue-500'}`}>
              <Play size={16} /> {run.loading ? 'Running…' : 'Run'}
            </button>
            <div className="text-xs text-zinc-500 flex items-center gap-1"><Beaker size={14} /> Strategies: {strategies.join(', ') || 'loading…'}</div>
          </div>
        </div>

        {/* Result card */}
        <div className="bg-zinc-900 border border-zinc-700 rounded-md p-4 space-y-3">
          <h2 className="text-lg font-semibold text-white">Result</h2>
          {run.error && <pre className="text-red-400 text-sm whitespace-pre-wrap">{run.error}</pre>}

          {/* AB side-by-side view */}
          {abResult && Array.isArray(abResult.runs) && abResult.runs.length > 0 ? (
            <div className="space-y-3">
              <div className="text-sm text-zinc-400">A/B comparison for <span className="text-zinc-200 font-medium">{abResult.query}</span></div>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                {abResult.runs.map((r: any) => (
                  <div key={r.strategyId} className="border border-zinc-700 bg-zinc-950 rounded-md p-3 space-y-2">
                    <div className="flex items-center justify-between">
                      <div className="text-white font-semibold">{r.strategyId}</div>
                      <span className="inline-flex items-center gap-1 text-xs text-zinc-400"><Timer size={12} /> {r.metrics?.durationMs ?? '-'}ms</span>
                    </div>
                    <div className="flex flex-wrap gap-2">
                      <Badge label="avgScore" value={typeof r.quality?.avgScore === 'number' ? r.quality.avgScore.toFixed(3) : '-'} />
                      <Badge label="diversity" value={typeof r.quality?.diversity === 'number' ? r.quality.diversity.toFixed(3) : '-'} />
                      <Badge label="coverage" value={typeof r.quality?.coverage === 'number' ? r.quality.coverage.toFixed(3) : '-'} />
                      <Badge label="candidates" value={r.metrics?.candidateCount ?? 0} />
                      <Badge label="namespaces" value={r.metrics?.namespacesCount ?? 0} />
                    </div>
                    <div className="text-xs text-zinc-400">Matches: <span className="text-zinc-200">{r.response?.results?.length || 0}</span></div>
                    {/* Top 3 results preview */}
                    <div className="mt-1 space-y-1">
                      {(r.response?.results || []).slice(0, 3).map((res: any, i: number) => (
                        <div key={i} className="text-xs bg-zinc-900 border border-zinc-800 rounded p-2">
                          <div className="flex items-center justify-between">
                            <div className="text-zinc-200 truncate">{res.firestoreMetadata?.filePath || res.pineconeMetadata?.filePath || res.chunkId}</div>
                            <span className="ml-2 text-amber-300">{typeof res.score === 'number' ? res.score.toFixed(3) : '-'}</span>
                          </div>
                          <div className="text-zinc-500 truncate">{(res.content || '').slice(0, 140)}</div>
                        </div>
                      ))}
                    </div>

                    {/* Model answer text, if present */}
                    {r.response?.llmAnalysis && (
                      <div className="mt-2 space-y-1">
                        <div className="text-xs text-zinc-400 flex items-center gap-2 flex-wrap">
                          <ProviderModelBadge provider={r.response.llmProvider} model={r.response.llmModel} />
                        </div>
                        <pre className="whitespace-pre-wrap break-words bg-zinc-900 border border-zinc-800 rounded-md p-2 text-xs">{r.response.llmAnalysis}</pre>
                      </div>
                    )}
                  </div>
                ))}
              </div>
            </div>
          ) : null}

          {/* Non-AB view: render markdown formatted output from structured JSON */}
          {!abResult && run.data && (
            <div className="space-y-3">

              {/* A/B: View as Markdown toggle */}
              {abResult && (
                <div className="space-y-2">
                  <div className="flex items-center justify-between">
                    <div className="text-sm text-zinc-400">A/B: View as Markdown</div>
                    <CopyMarkdownButton getContent={() => formatDbQueryToMarkdown({ mode: 'ab', result: abResult }, selectedModel)} />
                  </div>
                  <MarkdownContent payload={{ mode: 'ab', result: abResult }} defaultModel={selectedModel} />
                </div>
              )}

              <div className="flex items-center justify-between">
                <div className="text-sm text-zinc-400 flex items-center gap-2">
                  Rendered as Markdown
                  {run.data?.run?.response?.llmAnalysis ? (
                    <ProviderModelBadge provider={run.data.run.response.llmProvider} model={run.data.run.response.llmModel} />
                  ) : null}
                </div>
                <div className="flex items-center gap-2">
                  <CopyMarkdownButton getContent={() => formatDbQueryToMarkdown(run.data, selectedModel)} />
                  <SavePdfButton
                    title={`DB Query - ${category} (${new Date().toISOString().split('T')[0]})`}
                    getContent={() => formatDbQueryToMarkdown(run.data, selectedModel)}
                    userId={userId}
                    selectedProvider={selectedProvider}
                    selectedModel={selectedModel}
                    abMode={abMode}
                    strategyId={strategyId}
                    namespaces={namespaces}
                    query={query}
                    filtersJson={filtersJson}
                    category={category}
                  />
                </div>
              </div>
              <MarkdownContent payload={run.data} defaultModel={selectedModel} />
            </div>
          )}

          {/* AB: View as Markdown block moved outside so it renders when abResult exists */}
          {abResult && (
            <div className="space-y-2 mt-3">
              <div className="flex items-center justify-between">
                <div className="text-sm text-zinc-400">A/B: View as Markdown</div>
                <div className="flex items-center gap-2">
                  <CopyMarkdownButton getContent={() => formatDbQueryToMarkdown({ mode: 'ab', result: abResult }, selectedModel)} />
                  <SavePdfButton
                    title={`DB Query AB - ${category} (${new Date().toISOString().split('T')[0]})`}
                    getContent={() => formatDbQueryToMarkdown({ mode: 'ab', result: abResult }, selectedModel)}
                    userId={userId}
                    selectedProvider={selectedProvider}
                    selectedModel={selectedModel}
                    abMode={abMode}
                    strategyId={strategyId}
                    namespaces={namespaces}
                    query={query}
                    filtersJson={filtersJson}
                    category={category}
                  />
                </div>
              </div>
              <MarkdownContent payload={{ mode: 'ab', result: abResult }} defaultModel={selectedModel} />
            </div>
          )}

          {!run.error && !run.data && (
            <p className="text-sm text-zinc-400">Fill in the form and click Run to execute a query.</p>
          )}
        </div>
      </div>
    </main>
  );
}
