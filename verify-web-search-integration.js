/**
 * Comprehensive verification script for web search tool integration
 * Tests the complete flow from tool configuration to webhook execution
 */

const fetch = require('node-fetch');

async function verifyWebSearchIntegration() {
  console.log('🔍 COMPREHENSIVE WEB SEARCH INTEGRATION VERIFICATION');
  console.log('=' .repeat(60));

  const baseUrl = process.env.NEXTAUTH_URL || 'http://localhost:3000';
  const webhookSecret = process.env.ELEVENLABS_WEBHOOK_SECRET || 'pmo-webhook-secret-2024';

  console.log(`📍 Base URL: ${baseUrl}`);
  console.log(`🔐 Webhook Secret: ${webhookSecret.substring(0, 10)}...`);

  // Test 1: Verify webhook endpoint accessibility
  console.log('\n📋 TEST 1: Webhook Endpoint Accessibility');
  try {
    const testPayload = {
      query: 'test search query',
      context: 'verification test',
      searchPurpose: 'general'
    };

    const response = await fetch(`${baseUrl}/api/elevenlabs/web-search-webhook`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${webhookSecret}`
      },
      body: JSON.stringify(testPayload)
    });

    console.log(`   Status: ${response.status} ${response.statusText}`);
    
    if (response.ok) {
      const result = await response.json();
      console.log('   ✅ Webhook endpoint is accessible and working');
      console.log(`   📊 Response type: ${result.success ? 'Success' : 'Error'}`);
      console.log(`   📝 Message length: ${result.message?.length || 0} characters`);
    } else {
      const errorText = await response.text();
      console.log('   ❌ Webhook endpoint failed');
      console.log(`   📄 Error: ${errorText}`);
    }
  } catch (error) {
    console.log('   ❌ Webhook endpoint test failed');
    console.log(`   🚨 Error: ${error.message}`);
  }

  // Test 2: Verify authentication consistency
  console.log('\n📋 TEST 2: Authentication Consistency');
  try {
    // Test with correct auth
    const correctAuthResponse = await fetch(`${baseUrl}/api/elevenlabs/web-search-webhook`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${webhookSecret}`
      },
      body: JSON.stringify({ query: 'auth test' })
    });

    // Test with incorrect auth
    const incorrectAuthResponse = await fetch(`${baseUrl}/api/elevenlabs/web-search-webhook`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': 'Bearer wrong-secret'
      },
      body: JSON.stringify({ query: 'auth test' })
    });

    console.log(`   Correct Auth Status: ${correctAuthResponse.status}`);
    console.log(`   Incorrect Auth Status: ${incorrectAuthResponse.status}`);
    
    if (correctAuthResponse.ok && incorrectAuthResponse.status === 401) {
      console.log('   ✅ Authentication is working correctly');
    } else {
      console.log('   ❌ Authentication configuration issue detected');
    }
  } catch (error) {
    console.log('   ❌ Authentication test failed');
    console.log(`   🚨 Error: ${error.message}`);
  }

  // Test 3: Verify parameter validation
  console.log('\n📋 TEST 3: Parameter Validation');
  try {
    // Test missing required parameter
    const missingParamResponse = await fetch(`${baseUrl}/api/elevenlabs/web-search-webhook`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${webhookSecret}`
      },
      body: JSON.stringify({ context: 'test without query' })
    });

    console.log(`   Missing Query Status: ${missingParamResponse.status}`);
    
    if (missingParamResponse.status === 400) {
      console.log('   ✅ Parameter validation is working correctly');
    } else {
      console.log('   ❌ Parameter validation issue detected');
    }
  } catch (error) {
    console.log('   ❌ Parameter validation test failed');
    console.log(`   🚨 Error: ${error.message}`);
  }

  // Test 4: Verify CORS support
  console.log('\n📋 TEST 4: CORS Support');
  try {
    const corsResponse = await fetch(`${baseUrl}/api/elevenlabs/web-search-webhook`, {
      method: 'OPTIONS'
    });

    console.log(`   CORS Preflight Status: ${corsResponse.status}`);
    console.log(`   Access-Control-Allow-Origin: ${corsResponse.headers.get('Access-Control-Allow-Origin')}`);
    console.log(`   Access-Control-Allow-Methods: ${corsResponse.headers.get('Access-Control-Allow-Methods')}`);
    
    if (corsResponse.status === 200 && corsResponse.headers.get('Access-Control-Allow-Origin')) {
      console.log('   ✅ CORS is properly configured');
    } else {
      console.log('   ❌ CORS configuration issue detected');
    }
  } catch (error) {
    console.log('   ❌ CORS test failed');
    console.log(`   🚨 Error: ${error.message}`);
  }

  // Test 5: Verify response format
  console.log('\n📋 TEST 5: Response Format Verification');
  try {
    const formatTestResponse = await fetch(`${baseUrl}/api/elevenlabs/web-search-webhook`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${webhookSecret}`
      },
      body: JSON.stringify({
        query: 'JavaScript best practices',
        context: 'development documentation',
        searchPurpose: 'best_practices'
      })
    });

    if (formatTestResponse.ok) {
      const result = await response.json();
      const expectedFields = ['success', 'message', 'results', 'searchDetails'];
      const hasAllFields = expectedFields.every(field => field in result);
      
      console.log(`   Response has all expected fields: ${hasAllFields ? '✅' : '❌'}`);
      console.log(`   Expected: ${expectedFields.join(', ')}`);
      console.log(`   Actual: ${Object.keys(result).join(', ')}`);
      
      if (result.message && typeof result.message === 'string') {
        console.log('   ✅ Voice-optimized message format is correct');
      } else {
        console.log('   ❌ Voice-optimized message format issue');
      }
    } else {
      console.log('   ❌ Response format test failed due to request error');
    }
  } catch (error) {
    console.log('   ❌ Response format test failed');
    console.log(`   🚨 Error: ${error.message}`);
  }

  console.log('\n🏁 VERIFICATION COMPLETE');
  console.log('=' .repeat(60));
}

// Run the verification
verifyWebSearchIntegration().catch(console.error);
