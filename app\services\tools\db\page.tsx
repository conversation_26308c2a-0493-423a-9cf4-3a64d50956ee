"use client";

import React, { useCallback, useEffect, useMemo, useRef, useState } from "react";
import {
  collection,
  getDocs,
  query,
  where,
  limit as fsLimit,
  onSnapshot,
  getCountFromServer,
  DocumentData,
  Query,
} from "firebase/firestore";
import { db } from "components/firebase";
import { useSession, signIn } from "next-auth/react";
import {
  Database,
  Search,
  Download,
  ChevronRight,
  RefreshCcw,
  Loader2,
  Clipboard,
  ChevronLeft,
} from "lucide-react";

// Utility types
interface FileDoc {
  id: string; // documentId/namespace
  name?: string; // human friendly name
  category?: string;
  createdAt?: any;
}

interface ByteDocSummary {
  docId: string;
  title?: string;
  category?: string;
  chunkCount?: number;
}

interface ByteChunkDoc {
  id: string;
  content?: string;
  metadata?: Record<string, any>;
}

const PAGE_SIZE = 100; // load a reasonable page for debugging visibility
const SYS_ADMIN_EMAIL = "<EMAIL>"; // System admin can inspect any user

export default function FirestoreInspectorPage() {
  const { data: session, status } = useSession();

  // Target user scope (default to current user; admins can change)
  const [targetUser, setTargetUser] = useState<string>("");
  const [tempUserInput, setTempUserInput] = useState<string>("");

  // Left panel state
  const [files, setFiles] = useState<FileDoc[]>([]);
  const [loadingFiles, setLoadingFiles] = useState<boolean>(false);
  const [filesError, setFilesError] = useState<string | null>(null);

  // Structured search state
  type SearchMode = 'namespace' | 'name' | 'category';
  const [searchMode, setSearchMode] = useState<SearchMode>('name');
  const [search, setSearch] = useState<string>("");

  // Category mode results (from Firestore where query)
  const [categoryResults, setCategoryResults] = useState<FileDoc[] | null>(null);
  const [categoryLoading, setCategoryLoading] = useState<boolean>(false);
  const [categoryError, setCategoryError] = useState<string | null>(null);

  // Selection
  const [selectedDoc, setSelectedDoc] = useState<ByteDocSummary | null>(null);

  // Right panel state - chunks for the selected document
  const [chunks, setChunks] = useState<ByteChunkDoc[]>([]);
  const [selectedChunkId, setSelectedChunkId] = useState<string | null>(null);
  const [chunksLoading, setChunksLoading] = useState<boolean>(false);
  const [chunksError, setChunksError] = useState<string | null>(null);
  const unsubscribeRef = useRef<() => void>();

  const isAuthenticated = status === "authenticated";
  const isSystemAdmin = session?.user?.email === SYS_ADMIN_EMAIL;

  // Initialize target user
  useEffect(() => {
    if (isAuthenticated && !targetUser) {
      setTargetUser(session!.user!.email || "");
      setTempUserInput(session!.user!.email || "");
    }
  }, [isAuthenticated, session, targetUser]);

  // Fetch documents list (from users/{user}/files for doc-level overview)
  const fetchFiles = useCallback(async (userEmail: string) => {
    setLoadingFiles(true);
    setFilesError(null);
    try {
      const filesRef = collection(db, "users", userEmail, "files");
      const q = query(filesRef, fsLimit(500));
      const snap = await getDocs(q);
      const items: FileDoc[] = snap.docs.map((d) => {
        const data = d.data();
        return {
          id: (data.namespace as string) || d.id,
          name: (data.name as string) || data.title,
          category: data.category,
          createdAt: data.createdAt,
        };
      });
      // Remove duplicates by id (namespace)
      const unique = new Map<string, FileDoc>();
      for (const f of items) unique.set(f.id, f);
      const list = Array.from(unique.values());

      // Optionally compute counts lazily later
      setFiles(list);
    } catch (err: any) {
      console.error("Failed to fetch files list:", err);
      setFilesError(err.message || "Failed to load documents");
    } finally {
      setLoadingFiles(false);
    }
  }, []);

  // React to target user change
  useEffect(() => {
    if (!targetUser) return;
    fetchFiles(targetUser);
  }, [targetUser, fetchFiles]);

  // Run Firestore category query when in category mode
  useEffect(() => {
    if (searchMode !== 'category') { setCategoryResults(null); setCategoryError(null); setCategoryLoading(false); return; }
    const cat = search.trim();
    if (!cat) { setCategoryResults([]); return; }

    (async () => {
      setCategoryLoading(true);
      setCategoryError(null);
      try {
        const filesRef = collection(db, 'users', targetUser, 'files');
        const q = query(filesRef, where('category', '==', cat));
        const snap = await getDocs(q);
        const items: FileDoc[] = snap.docs.map((d) => {
          const data = d.data();
          return {
            id: (data.namespace as string) || d.id,
            name: (data.name as string) || data.title,
            category: data.category,
            createdAt: data.createdAt,
          };
        });
        const unique = new Map<string, FileDoc>();
        for (const f of items) unique.set(f.id, f);
        setCategoryResults(Array.from(unique.values()));
      } catch (err: any) {
        console.warn('Category query failed', err);
        setCategoryError(err?.message || 'Category query failed');
        setCategoryResults([]);
      } finally {
        setCategoryLoading(false);
      }
    })();
  }, [search, targetUser, searchMode]);

  // Compute filtered files
  const filteredFiles = useMemo(() => {
    const term = search.trim().toLowerCase();

    // Category mode uses Firestore results if available
    if (searchMode === 'category') {
      if (categoryResults) return categoryResults;
      // While loading or empty query, show nothing or all files based on UX; choose none until query returns
      return [];
    }

    // Client-side filter for name/namespace modes
    if (!term) return files;
    return files.filter((f) => {
      if (searchMode === 'namespace') return f.id.toLowerCase().includes(term);
      if (searchMode === 'name') return (f.name || '').toLowerCase().includes(term);
      // default fallback
      return (
        f.id.toLowerCase().includes(term) ||
        (f.name || '').toLowerCase().includes(term) ||
        (f.category || '').toLowerCase().includes(term)
      );
    });
  }, [files, search, categoryResults, searchMode]);

  // Helper: get chunk count for a docId (tries multiple field paths)
  const getChunkCount = useCallback(async (userEmail: string, docId: string) => {
    const fieldsToTry = [
      "metadata.doc_id",
      "metadata.namespace",
      "metadata.documentId",
      "doc_id",
      "namespace",
      "metadata.fileId",
      "metadata.file_id",
    ];
    try {
      const col = collection(db, "users", userEmail, "byteStoreCollection");
      for (const field of fieldsToTry) {
        try {
          const q = query(col, where(field as any, "==", docId));
          const countSnap = await getCountFromServer(q as unknown as Query<DocumentData>);
          const count = countSnap.data().count || 0;
          if (count > 0) {
            try { console.log(`[DBI] Count for ${docId} using ${field}:`, count); } catch {}
            return count;
          }
        } catch (inner) {
          try { console.warn(`[DBI] getChunkCount field failed`, { field, error: inner }); } catch {}
        }
      }
      try { console.log(`[DBI] No chunks found for doc`, { userEmail, docId }); } catch {}
      return 0;
    } catch (e) {
      console.warn("getChunkCount failed, returning 0", e);
      return 0;
    }
  }, []);

  // Select a document (namespace) and load its chunks
  const selectDocument = useCallback(
    async (doc: FileDoc) => {
      const summary: ByteDocSummary = {
        docId: doc.id,
        title: doc.name,
        category: doc.category,
      };

      setSelectedDoc(summary);
      setSelectedChunkId(null);
      setChunks([]);
      setChunksError(null);
      setChunksLoading(true);

      // Clean up previous subscription
      if (unsubscribeRef.current) {
        try { unsubscribeRef.current(); } catch {}
        unsubscribeRef.current = undefined;
      }

      try {
        // Fetch chunk count lazily
        getChunkCount(targetUser, doc.id).then((count) =>
          setSelectedDoc((prev) => (prev ? { ...prev, chunkCount: count } : prev))
        );

        const col = collection(db, "users", targetUser, "byteStoreCollection");

        // We'll attempt several field paths to ensure compatibility with varying metadata schemas
        const fieldsToTry = [
          "metadata.doc_id",
          "metadata.namespace",
          "metadata.documentId",
          "doc_id",
          "namespace",
          "metadata.fileId",
          "metadata.file_id",
        ];

        let firstSnap: any = null;
        let lastQuery: any = null;
        for (const field of fieldsToTry) {
          try {
            const q = query(col, where(field as any, "==", doc.id), fsLimit(PAGE_SIZE));
            lastQuery = { field };
            const snap = await getDocs(q);
            if (!snap.empty) {
              firstSnap = snap;
              console.log(`[DBI] Loaded chunks for`, { field, count: snap.size, user: targetUser, docId: doc.id });
              // Subscribe for real-time updates for this doc namespace using the working field
              unsubscribeRef.current = onSnapshot(q, (ss) => {
                const updated: ByteChunkDoc[] = ss.docs.map((d) => ({ id: d.id, ...(d.data() as any) }));
                setChunks(updated);
              });
              break;
            } else {
              console.log(`[DBI] No chunks using field`, { field, user: targetUser, docId: doc.id });
            }
          } catch (inner) {
            console.warn(`[DBI] Query failed for field`, { field, error: inner });
          }
        }

        if (firstSnap) {
          const chunkDocs: ByteChunkDoc[] = firstSnap.docs.map((d: any) => ({ id: d.id, ...(d.data() as any) }));
          setChunks(chunkDocs);
        } else {
          setChunks([]);
          setChunksError(`No chunks found for ${doc.id}. Tried fields: ${fieldsToTry.join(", ")}`);
          console.warn(`[DBI] No chunks found after trying multiple fields`, { user: targetUser, docId: doc.id, lastQuery });
        }
      } catch (err: any) {
        console.error("Failed to fetch chunks:", err);
        setChunksError(err.message || "Failed to load chunks");
      } finally {
        setChunksLoading(false);
      }
    },
    [getChunkCount, targetUser]
  );

  // Cleanup subscription on unmount
  useEffect(() => {
    return () => {
      if (unsubscribeRef.current) {
        try { unsubscribeRef.current(); } catch {}
      }
    };
  }, []);

  // Export selected document (all currently loaded chunks) as JSON
  const handleExport = useCallback(() => {
    if (!selectedDoc) return;
    const data = {
      docId: selectedDoc.docId,
      title: selectedDoc.title,
      category: selectedDoc.category,
      chunkCount: selectedDoc.chunkCount ?? chunks.length,
      chunks: chunks.map((c) => ({ id: c.id, content: c.content, metadata: c.metadata })),
    };
    const blob = new Blob([JSON.stringify(data, null, 2)], { type: "application/json" });
    const url = URL.createObjectURL(blob);
    const a = document.createElement("a");
    a.href = url;
    a.download = `${selectedDoc.docId}.json`;
    a.click();
    URL.revokeObjectURL(url);
  }, [selectedDoc, chunks]);

  // Copy helper
  const copyToClipboard = useCallback(async (text: string) => {
    try {
      await navigator.clipboard.writeText(text);
      alert("Copied to clipboard");
    } catch {}
  }, []);

  // Render helpers
  const renderValue = (value: any, depth = 0) => {
    const pad = depth * 8;
    if (value === null || value === undefined) return <span className="text-zinc-400">null</span>;
    if (typeof value === "string") return <span className="break-words text-zinc-100">{value}</span>;
    if (typeof value === "number" || typeof value === "boolean") return <span className="text-emerald-300">{String(value)}</span>;
    if (Array.isArray(value)) {
      return (
        <div className="space-y-1">
          {value.map((v, i) => (
            <div key={i} style={{ paddingLeft: pad + 8 }} className="pl-2 border-l border-zinc-700">
              <span className="text-zinc-400 mr-2">[{i}]</span>
              {renderValue(v, depth + 1)}
            </div>
          ))}
        </div>
      );
    }
    if (typeof value === "object") {
      return (
        <div className="space-y-1">
          {Object.entries(value).map(([k, v]) => (
            <div key={k} style={{ paddingLeft: pad + 8 }} className="pl-2 border-l border-zinc-700">
              <span className="text-blue-300 mr-2">{k}</span>
              {renderValue(v, depth + 1)}
            </div>
          ))}
        </div>
      );
    }
    return <span className="text-zinc-400">{String(value)}</span>;
  };

  if (!isAuthenticated) {
    return (
      <div className="min-h-screen bg-zinc-950 text-zinc-100 flex items-center justify-center p-8">
        <div className="bg-zinc-900 p-8 rounded-lg border border-zinc-700 max-w-lg w-full text-center">
          <Database className="mx-auto text-blue-400 mb-3" size={36} />
          <h1 className="text-2xl font-bold mb-2 text-white">Database Inspector</h1>
          <p className="text-zinc-400 mb-6">Please sign in to access the Firestore inspector.</p>
          <button
            onClick={() => signIn("google", { callbackUrl: "/services/tools/db" })}
            className="px-4 py-2 bg-blue-600 hover:bg-blue-500 rounded-md"
          >
            Sign in with Google
          </button>
        </div>
      </div>
    );
  }

  return (
    <main className="min-h-screen bg-zinc-950 text-zinc-100 p-4 md:p-6">
      <div className="max-w-7xl mx-auto">
        {/* Header */}
        <div className="flex items-center justify-between mb-4">
          <div className="flex items-center gap-3">
            <Database className="text-blue-400" size={28} />
            <div>
              <h1 className="text-2xl font-bold text-white">Firestore DB Inspector</h1>
              <p className="text-sm text-zinc-400">Path: users/{targetUser || "?"}/byteStoreCollection</p>
            </div>
          </div>

          <div className="flex items-center gap-2">
            {selectedDoc && (
              <button onClick={handleExport} className="flex items-center gap-2 px-3 py-2 bg-zinc-800 hover:bg-zinc-700 rounded-md">
                <Download size={16} className="text-green-400" /> Export JSON
              </button>
            )}
            <button onClick={() => targetUser && fetchFiles(targetUser)} className="flex items-center gap-2 px-3 py-2 bg-zinc-800 hover:bg-zinc-700 rounded-md">
              <RefreshCcw size={16} /> Refresh
            </button>
          </div>
        </div>

        {/* Target user selector for admins */}
        <div className="bg-zinc-900 border border-zinc-700 rounded-md p-3 mb-4">
          <div className="flex flex-col md:flex-row md:items-center gap-3">
            <div className="flex-1">
              <label className="text-xs uppercase text-zinc-400">User scope</label>
              <div className="flex gap-2 mt-1">
                <input
                  type="email"
                  value={tempUserInput}
                  onChange={(e) => setTempUserInput(e.target.value)}
                  disabled={!isSystemAdmin}
                  className="w-full px-3 py-2 bg-zinc-800 border border-zinc-700 rounded-md text-zinc-100"
                  placeholder="<EMAIL>"
                />
                <button
                  disabled={!isSystemAdmin}
                  onClick={() => setTargetUser(tempUserInput.trim())}
                  className={`px-3 py-2 rounded-md ${isSystemAdmin ? "bg-blue-600 hover:bg-blue-500" : "bg-zinc-700"}`}
                >
                  Apply
                </button>
              </div>
              {!isSystemAdmin && (
                <p className="text-xs text-zinc-500 mt-1">Only system admin can change the user scope. You are viewing your own data.</p>
              )}
            </div>
          </div>
        </div>

        {/* Split layout */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          {/* Left: Document list */}
          <div className="md:col-span-1 bg-zinc-900 border border-zinc-700 rounded-md overflow-hidden flex flex-col">
            <div className="p-3 border-b border-zinc-800 flex items-center gap-2">
              <Search size={16} className="text-zinc-400" />
              <select
                value={searchMode}
                onChange={(e) => { setSearchMode(e.target.value as any); setCategoryResults(null); }}
                className="px-2 py-2 bg-zinc-800 border border-amber-400 rounded-md text-sm italic text-amber-400"
                aria-label="Search mode"
                title="Search Mode"
              >
                <option value="namespace">Namespace</option>
                <option value="name">Name</option>
                <option value="category">Category</option>
              </select>
              <input
                value={search}
                onChange={(e) => setSearch(e.target.value)}
                placeholder={searchMode === 'category' ? 'Enter category (exact match)' : (searchMode === 'namespace' ? 'Search namespace' : 'Search name')}
                className="flex-1 bg-transparent outline-none text-sm"
              />
            </div>

            {loadingFiles && (
              <div className="p-4 text-sm text-zinc-400 flex items-center gap-2"><Loader2 className="animate-spin" size={16} /> Loading documents…</div>
            )}
            {filesError && (
              <div className="p-4 text-sm text-red-400">{filesError}</div>
            )}

            <div className="overflow-y-auto" style={{ maxHeight: "calc(100vh - 280px)" }}>
              {filteredFiles.map((f) => (
                <button
                  key={f.id}
                  onClick={() => selectDocument(f)}
                  className={`w-full text-left px-4 py-3 border-b border-zinc-800 hover:bg-zinc-800/60 ${selectedDoc?.docId === f.id ? "bg-zinc-800" : ""}`}
                >
                  <div className="flex items-center justify-between">
                    <div>
                      <div className="text-white font-medium text-sm truncate">{f.name || f.id}</div>
                      <div className="text-xs text-zinc-400 truncate">{f.id}</div>
                    </div>
                    <ChevronRight size={16} className="text-zinc-500" />
                  </div>
                  {selectedDoc?.docId === f.id && selectedDoc?.chunkCount !== undefined && (
                    <div className="text-xs text-zinc-500 mt-1">Chunks: {selectedDoc.chunkCount}</div>
                  )}
                </button>
              ))}

              {!loadingFiles && filteredFiles.length === 0 && (
                <div className="p-4 text-sm text-zinc-400">No documents found.</div>
              )}
            </div>
          </div>

          {/* Right: Field inspector */}
          <div className="md:col-span-2 bg-zinc-900 border border-zinc-700 rounded-md min-h-[60vh]">
            {!selectedDoc ? (
              <div className="p-6 text-zinc-400">Select a document from the left to inspect its chunks stored in byteStoreCollection.</div>
            ) : (
              <div className="flex flex-col h-full">
                {/* Breadcrumb */}
                <div className="p-3 border-b border-zinc-800 text-xs text-amber-400 flex items-center gap-1">
                  <span>users</span>
                  <ChevronRight size={12} />
                  <span>{targetUser}</span>
                  <ChevronRight size={12} />
                  <span>byteStoreCollection</span>
                  <ChevronRight size={12} />
                  <span className="text-blue-400">{selectedDoc.docId}</span>
                </div>

                {/* Selected summary */}
                <div className="p-4 border-b border-zinc-800 flex items-center justify-between">
                  <div>
                    <div className="text-white text-lg font-semibold">{selectedDoc.title || selectedDoc.docId}</div>
                    <div className="text-xs text-zinc-500">Doc ID: {selectedDoc.docId} {selectedDoc.chunkCount !== undefined && `• ${selectedDoc.chunkCount} chunks`}</div>
                  </div>
                  <div className="flex items-center gap-2">
                    <button onClick={handleExport} className="flex items-center gap-2 px-3 py-2 bg-zinc-800 hover:bg-zinc-700 rounded-md text-sm">
                      <Download size={14} className="text-green-400" /> Export
                    </button>
                  </div>
                </div>

                {/* Chunks list and inspector */}
                <div className="grid grid-cols-1 xl:grid-cols-3 gap-0 flex-1 overflow-hidden">
                  {/* Chunks list */}
                  <div className="xl:col-span-1 border-r border-zinc-800 overflow-y-auto" style={{ maxHeight: "calc(100vh - 340px)" }}>
                    {chunksLoading && (
                      <div className="p-4 text-sm text-zinc-400 flex items-center gap-2"><Loader2 className="animate-spin" size={16} /> Loading chunks…</div>
                    )}
                    {chunksError && <div className="p-4 text-sm text-red-400">{chunksError}</div>}
                    {chunks.map((c) => (
                      <button
                        key={c.id}
                        onClick={() => setSelectedChunkId(c.id)}
                        className={`w-full text-left px-4 py-3 border-b border-zinc-800 hover:bg-zinc-800/60 ${selectedChunkId === c.id ? "bg-zinc-800" : ""}`}
                      >
                        <div className="flex items-center justify-between">
                          <div>
                            <div className="text-white text-sm font-medium">{c.id}</div>
                            <div className="text-xs text-zinc-500 truncate">{(c.metadata?.chunk_index ?? "") && `Index: ${c.metadata?.chunk_index}`}</div>
                          </div>
                          <ChevronRight size={16} className="text-zinc-500" />
                        </div>
                      </button>
                    ))}
                    {!chunksLoading && chunks.length === 0 && (
                      <div className="p-4 text-sm text-zinc-400">No chunks found for this document.</div>
                    )}
                  </div>

                  {/* Inspector */}
                  <div className="xl:col-span-2 overflow-y-auto p-4" style={{ maxHeight: "calc(100vh - 340px)" }}>
                    {!selectedChunkId ? (
                      <div className="text-zinc-400">Select a chunk to view its fields.</div>
                    ) : (
                      (() => {
                        const chunk = chunks.find((c) => c.id === selectedChunkId);
                        if (!chunk) return <div className="text-zinc-400">Chunk not found.</div>;
                        return (
                          <div className="space-y-4">
                            <div className="flex items-center justify-between">
                              <div className="flex items-center gap-2">
                                <button onClick={() => setSelectedChunkId(null)} className="px-2 py-1 bg-zinc-800 hover:bg-zinc-700 rounded-md text-xs flex items-center gap-1">
                                  <ChevronLeft size={14} /> Back
                                </button>
                                <h3 className="text-white font-semibold">Chunk: {chunk.id}</h3>
                              </div>
                              <div className="flex items-center gap-2">
                                <button onClick={() => copyToClipboard(chunk.content || "")} className="px-2 py-1 bg-zinc-800 hover:bg-zinc-700 rounded-md text-xs flex items-center gap-1">
                                  <Clipboard size={14} /> Copy content
                                </button>
                                <button onClick={() => copyToClipboard(JSON.stringify(chunk.metadata || {}, null, 2))} className="px-2 py-1 bg-zinc-800 hover:bg-zinc-700 rounded-md text-xs flex items-center gap-1">
                                  <Clipboard size={14} /> Copy metadata
                                </button>
                              </div>
                            </div>

                            <section>
                              <h4 className="text-sm text-zinc-400 mb-2">Content</h4>
                              <div className="bg-zinc-950 border border-zinc-800 rounded-md p-3 text-sm whitespace-pre-wrap">
                                {chunk.content ? chunk.content : <span className="text-zinc-500">(empty)</span>}
                              </div>
                            </section>

                            <section>
                              <h4 className="text-sm text-zinc-400 mb-2">Metadata</h4>
                              <div className="bg-zinc-950 border border-zinc-800 rounded-md p-3 text-sm">
                                {renderValue(chunk.metadata || {})}
                              </div>
                            </section>
                          </div>
                        );
                      })()
                    )}
                  </div>
                </div>
              </div>
            )}
          </div>
        </div>
      </div>
    </main>
  );
}

