/**
 * createDocumentationTool.test.ts
 * 
 * Test file demonstrating usage of the createDocumentationTool wrapper
 */

import { createDocumentationTool, generateDocumentation } from './createDocumentationTool';
import type { CreateDocumentationToolOptions } from './createDocumentationTool';

/**
 * Example 1: Basic documentation generation with defaults
 */
async function testBasicDocumentation() {
  console.log('=== Test 1: Basic Documentation Generation ===');
  
  const result = await generateDocumentation(
    '<EMAIL>',
    'Codebase',
    'Summarize the authentication system and user management features'
  );

  if (result.success) {
    console.log('✅ Success!');
    console.log('Generated Title:', result.generatedTitle);
    console.log('Strategy Used:', result.strategy);
    console.log('Total Matches:', result.metrics?.totalMatches);
    console.log('Query Duration:', result.metrics?.queryDurationMs, 'ms');
    console.log('Markdown Length:', result.markdownContent.length, 'characters');
    console.log('First 200 chars:', result.markdownContent.substring(0, 200));
  } else {
    console.log('❌ Failed:', result.error);
  }
}

/**
 * Example 2: Advanced configuration with PDF generation
 */
async function testAdvancedDocumentation() {
  console.log('\n=== Test 2: Advanced Documentation with PDF ===');
  
  const options: CreateDocumentationToolOptions = {
    userId: '<EMAIL>',
    category: 'Codebase',
    query: 'Explain the database query system architecture and implementation',
    strategy: 'standalone',
    topK: 3,
    modelProvider: 'google',
    modelName: 'gemini-2.5-pro',
    generatePDF: true,
    customTitle: 'Database Query System Architecture Guide'
  };

  const result = await createDocumentationTool(options);

  if (result.success) {
    console.log('✅ Success!');
    console.log('Strategy Used:', result.strategy);
    console.log('Total Matches:', result.metrics?.totalMatches);
    console.log('Namespaces Queried:', result.metrics?.namespacesQueried);
    
    if (result.pdfResult) {
      console.log('📄 PDF Generated!');
      console.log('Document ID:', result.pdfResult.documentId);
      console.log('Download URL:', result.pdfResult.downloadUrl);
    }
  } else {
    console.log('❌ Failed:', result.error);
  }
}

/**
 * Example 3: A/B Testing multiple strategies
 */
async function testABTesting() {
  console.log('\n=== Test 3: A/B Testing Multiple Strategies ===');
  
  const options: CreateDocumentationToolOptions = {
    userId: '<EMAIL>',
    category: 'Codebase',
    query: 'How does the PMO document processing system work?',
    abTest: true,
    abStrategies: ['standalone', 'content-selector'],
    topK: 2,
    generatePDF: false
  };

  const result = await createDocumentationTool(options);

  if (result.success) {
    console.log('✅ A/B Test Success!');
    console.log('Strategy Used:', result.strategy);
    
    // Access A/B test specific data
    const abResult = result.rawData as any;
    if (abResult.runs) {
      console.log('Strategies Compared:', abResult.runs.map((r: any) => r.strategyId));
      abResult.runs.forEach((run: any, idx: number) => {
        console.log(`Strategy ${idx + 1} (${run.strategyId}):`);
        console.log('  - Duration:', run.metrics?.durationMs, 'ms');
        console.log('  - Avg Score:', run.quality?.avgScore?.toFixed(3));
        console.log('  - Total Matches:', run.response?.stats?.totalMatches);
      });
    }
  } else {
    console.log('❌ A/B Test Failed:', result.error);
  }
}

/**
 * Example 4: Filtered query with specific namespaces
 */
async function testFilteredQuery() {
  console.log('\n=== Test 4: Filtered Query with Namespaces ===');

  const options: CreateDocumentationToolOptions = {
    userId: '<EMAIL>',
    category: 'Codebase',
    query: 'Show me the API endpoints and their implementations',
    namespaces: ['api-routes', 'backend-services'],
    strategy: 'two-stage',
    topK: 5,
    filters: {
      codeEntityType: ['function', 'class'],
      filePath: { pattern: '.*\\.ts$', flags: 'i' }
    }
  };

  const result = await createDocumentationTool(options);

  if (result.success) {
    console.log('✅ Filtered Query Success!');
    console.log('Namespaces Queried:', result.metrics?.namespacesQueried);
    console.log('Total Matches:', result.metrics?.totalMatches);
    console.log('Strategy Used:', result.strategy);
  } else {
    console.log('❌ Filtered Query Failed:', result.error);
  }
}

/**
 * Example 5: Clean output vs Technical metadata comparison
 */
async function testMetadataOptions() {
  console.log('\n=== Test 5: Clean vs Technical Metadata Output ===');

  const baseOptions = {
    userId: '<EMAIL>',
    category: 'Codebase',
    query: 'Explain the authentication system architecture'
  };

  // Test with clean output (default)
  console.log('\n--- Clean Output (removeTechnicalMetadata: true) ---');
  const cleanResult = await createDocumentationTool({
    ...baseOptions,
    removeTechnicalMetadata: true
  });

  if (cleanResult.success) {
    console.log('✅ Clean output generated');
    console.log('Content length:', cleanResult.markdownContent.length);
    console.log('First 150 chars:', cleanResult.markdownContent.substring(0, 150));
    console.log('Contains "DB Query Result":', cleanResult.markdownContent.includes('DB Query Result'));
    console.log('Contains "Top Matches":', cleanResult.markdownContent.includes('Top Matches'));
  }

  // Test with technical metadata
  console.log('\n--- Technical Output (removeTechnicalMetadata: false) ---');
  const technicalResult = await createDocumentationTool({
    ...baseOptions,
    removeTechnicalMetadata: false
  });

  if (technicalResult.success) {
    console.log('✅ Technical output generated');
    console.log('Content length:', technicalResult.markdownContent.length);
    console.log('First 150 chars:', technicalResult.markdownContent.substring(0, 150));
    console.log('Contains "DB Query Result":', technicalResult.markdownContent.includes('DB Query Result'));
    console.log('Contains "Top Matches":', technicalResult.markdownContent.includes('Top Matches'));
  }
}

/**
 * Example 6: Error handling demonstration
 */
async function testErrorHandling() {
  console.log('\n=== Test 6: Error Handling ===');

  // Test with missing required parameters
  const result = await createDocumentationTool({
    userId: '',
    category: '',
    query: ''
  });

  if (!result.success) {
    console.log('✅ Error handling works correctly');
    console.log('Error message:', result.error);
  } else {
    console.log('❌ Error handling failed - should have returned error');
  }
}

/**
 * Run all tests
 */
async function runAllTests() {
  console.log('🚀 Starting createDocumentationTool Tests\n');
  
  try {
    await testBasicDocumentation();
    await testAdvancedDocumentation();
    await testABTesting();
    await testFilteredQuery();
    await testMetadataOptions();
    await testErrorHandling();

    console.log('\n✅ All tests completed!');
  } catch (error) {
    console.error('\n❌ Test suite failed:', error);
  }
}

// Export for use in other files
export {
  testBasicDocumentation,
  testAdvancedDocumentation,
  testABTesting,
  testFilteredQuery,
  testMetadataOptions,
  testErrorHandling,
  runAllTests
};

// Run tests if this file is executed directly
if (require.main === module) {
  runAllTests();
}

/**
 * Usage Examples for AI Agents:
 * 
 * 1. Simple Documentation Generation:
 * ```typescript
 * const result = await generateDocumentation(
 *   '<EMAIL>',
 *   'Codebase',
 *   'Explain the authentication system'
 * );
 * ```
 * 
 * 2. Generate Documentation with PDF:
 * ```typescript
 * const result = await createDocumentationTool({
 *   userId: '<EMAIL>',
 *   category: 'Codebase',
 *   query: 'How does the payment system work?',
 *   generatePDF: true,
 *   customTitle: 'Payment System Documentation',
 *   removeTechnicalMetadata: true // Clean output for documentation
 * });
 * ```
 * 
 * 3. Compare Multiple Strategies:
 * ```typescript
 * const result = await createDocumentationTool({
 *   userId: '<EMAIL>',
 *   category: 'Codebase',
 *   query: 'Analyze the database layer',
 *   abTest: true,
 *   abStrategies: ['standalone', 'two-stage', 'hybrid-inverted']
 * });
 * ```
 * 
 * 4. Targeted Query with Filters:
 * ```typescript
 * const result = await createDocumentationTool({
 *   userId: '<EMAIL>',
 *   category: 'Codebase',
 *   query: 'Find React components',
 *   namespaces: ['frontend'],
 *   filters: {
 *     filePath: { pattern: '.*\\.tsx?$' },
 *     codeEntityType: 'component'
 *   }
 * });
 * ```
 *
 * 5. Control Technical Metadata Output:
 * ```typescript
 * // Clean output for documentation (default)
 * const cleanResult = await createDocumentationTool({
 *   userId: '<EMAIL>',
 *   category: 'Codebase',
 *   query: 'Explain the database layer',
 *   removeTechnicalMetadata: true // Only Answer content
 * });
 *
 * // Full technical output for debugging
 * const technicalResult = await createDocumentationTool({
 *   userId: '<EMAIL>',
 *   category: 'Codebase',
 *   query: 'Explain the database layer',
 *   removeTechnicalMetadata: false // Include all metadata, scores, matches
 * });
 * ```
 */
