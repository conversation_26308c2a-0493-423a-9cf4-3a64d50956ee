export const runtime = 'nodejs';
export const maxDuration = 60;

import { NextRequest, NextResponse } from 'next/server';
import { saveTranscript, TranscriptMetadata } from '../../../../lib/utils/transcriptUtils';
import { TranscriptMessage } from '../../../../components/PMO/TranscriptPanel';
import {
  generateMeetingDocument,
  determineMeetingDocumentCategory,
  MeetingDocumentGenerationResult
} from '../../../../lib/tools/meetingDocumentGenerator';
import { createPMORecordFromForm, updatePMORecord } from '../../../../lib/firebase/pmoCollection';
// import { getTeamIdFromAgentType } from '../../../../lib/utils/teamNameUtils';
import { mapAgentTypeToAgenticTeamId, AgenticTeamId } from '../../../../lib/agents/pmo/PMOInterfaces';


import { getUserAgentByUserGeneratedId } from '../../../../lib/firebase/userAgents';

/**
 * Webhook endpoint for ElevenLabs agents to save meeting summaries
 * This endpoint is called by the voice agent when the conversation is ending
 * to save the transcript and optionally generate documents
 *
 * This replaces the need for workspace-level post-call webhooks by allowing
 * the agent to proactively save the meeting content before ending the call.
 */
export async function POST(request: NextRequest) {
  const startTime = Date.now();
  const EARLY_ACK_TIMEOUT = 45000; // 45 seconds - leave buffer for Vercel's 60s limit

  try {
    console.log('[SAVE_MEETING_WEBHOOK] Received save meeting summary request from voice agent');
    console.log('[SAVE_MEETING_WEBHOOK] Request headers:', Object.fromEntries(request.headers.entries()));

    // Verify webhook authentication
    const authHeader = request.headers.get('authorization');
    const expectedAuth = `Bearer ${process.env.ELEVENLABS_WEBHOOK_SECRET || 'pmo-webhook-secret-2024'}`;

    if (authHeader !== expectedAuth) {
      console.error('[SAVE_MEETING_WEBHOOK] Unauthorized webhook request');
      console.error('[SAVE_MEETING_WEBHOOK] Received auth:', authHeader);
      console.error('[SAVE_MEETING_WEBHOOK] Expected auth:', expectedAuth);
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Helper function to check if we should return early
    const shouldReturnEarly = () => {
      const elapsed = Date.now() - startTime;
      return elapsed > EARLY_ACK_TIMEOUT;
    };

    // Parse request body
    const body = await request.json();
    console.log('[SAVE_MEETING_WEBHOOK] Request body:', JSON.stringify(body, null, 2));

    // Extract data from ElevenLabs webhook format
    const {
      summary,
      action_items,
      document_title,
      generate_document = true,
      // Web search results from the meeting
      web_search_results = [],
      search_queries = [],
      research_findings = '',
      // ElevenLabs provides these automatically
      agent_id,
      conversation_id,
      user_id = process.env.NEXT_PUBLIC_SYS_ADMIN || '<EMAIL>', // Use system admin instead of 'system'
      conversation_history = [],
      // ✅ Extract transcript metadata containing selected document categories
      transcript_metadata = {},
      // ✅ Extract conversation initiation metadata (passed from UI)
      conversation_initiation_client_data = {}
    } = body;

    console.log('[SAVE_MEETING_WEBHOOK] Processing meeting summary:', {
      agent_id,
      conversation_id,
      user_id,
      summary_length: summary?.length || 0,
      has_action_items: !!action_items,
      generate_document,
      conversation_history_length: conversation_history?.length || 0,
      web_search_results_count: web_search_results?.length || 0,
      search_queries_count: search_queries?.length || 0,
      has_research_findings: !!research_findings,
      transcript_metadata: transcript_metadata
    });

    // ✅ Extract selected document categories from transcript metadata AND conversation initiation data
    console.log('[SAVE_MEETING_WEBHOOK] Transcript metadata received:', transcript_metadata);
    console.log('[SAVE_MEETING_WEBHOOK] Conversation initiation client data received:', conversation_initiation_client_data);

    // Extract category with priority logic (conversation initiation data takes priority)
    let selectedDocumentCategory = conversation_initiation_client_data?.documentCategory ||
                                  transcript_metadata?.documentCategory ||
                                  transcript_metadata?.finalCategory ||
                                  transcript_metadata?.category;

    // If no full category found, try to extract from other metadata fields
    if (!selectedDocumentCategory) {
      // Try conversation initiation data first
      if (conversation_initiation_client_data?.selectedDocuments && conversation_initiation_client_data.selectedDocuments.length > 0) {
        const firstDoc = conversation_initiation_client_data.selectedDocuments[0];
        selectedDocumentCategory = firstDoc.category;
      }
      // Then try transcript metadata
      else if (transcript_metadata) {
        if (transcript_metadata.metadata?.category) {
          selectedDocumentCategory = transcript_metadata.metadata.category;
        } else if (transcript_metadata.selectedDocuments && transcript_metadata.selectedDocuments.length > 0) {
          const firstDoc = transcript_metadata.selectedDocuments[0];
          selectedDocumentCategory = firstDoc.category || firstDoc.metadata?.category;
        }
      }
    }

    const selectedDocumentId = transcript_metadata?.documentId;
    const metadataAgentType = conversation_initiation_client_data?.agentType || transcript_metadata?.agentType;

    console.log('[SAVE_MEETING_WEBHOOK] Extracted from transcript metadata:', {
      selectedDocumentCategory,
      selectedDocumentId,
      metadataAgentType,
      hasTranscriptMetadata: !!transcript_metadata,
      transcriptMetadataKeys: transcript_metadata ? Object.keys(transcript_metadata) : []
    });

    // Validate required data
    if (!summary) {
      console.error('[SAVE_MEETING_WEBHOOK] Missing required summary');
      return NextResponse.json({
        error: 'Missing required field: summary'
      }, { status: 400 });
    }

    // Create transcript messages from the summary and action items
    // Note: ElevenLabs doesn't provide full conversation history in tool calls,
    // so we create a structured summary document instead
    const transcriptMessages: TranscriptMessage[] = [
      {
        role: 'assistant',
        content: `Meeting Summary Generated by Agent`,
        timestamp: new Date(Date.now() - 60000) // 1 minute ago
      },
      {
        role: 'assistant',
        content: `Summary: ${summary}`,
        timestamp: new Date(Date.now() - 30000) // 30 seconds ago
      }
    ];

    // Add action items if provided
    if (action_items && action_items.trim().length > 0) {
      transcriptMessages.push({
        role: 'assistant',
        content: `Action Items: ${action_items}`,
        timestamp: new Date(Date.now() - 45000) // 45 seconds ago
      });
    }

    // Add web search results and research findings if available
    if (search_queries && search_queries.length > 0) {
      transcriptMessages.push({
        role: 'assistant',
        content: `Web Search Queries Performed: ${search_queries.join(', ')}`,
        timestamp: new Date(Date.now() - 30000) // 30 seconds ago
      });
    }

    if (research_findings && research_findings.trim().length > 0) {
      transcriptMessages.push({
        role: 'assistant',
        content: `Research Findings: ${research_findings}`,
        timestamp: new Date(Date.now() - 15000) // 15 seconds ago
      });
    }

    // Add final summary message
    transcriptMessages.push({
      role: 'assistant',
      content: `Meeting documentation completed. Summary, action items, and research findings have been saved.`,
      timestamp: new Date()
    });

    console.log('[SAVE_MEETING_WEBHOOK] Created transcript with', transcriptMessages.length, 'messages');

    // ✅ STEP 1: Extract agent type from transcript metadata (same as DocumentContextPanel.tsx)
    // This should come from the conversation context, not from ElevenLabs agent ID
    let finalAgentType = metadataAgentType;
    let mappedAgentId = 'unknown';

    console.log('[SAVE_MEETING_WEBHOOK] Starting agent type resolution:', {
      agent_id,
      metadataAgentType,
      user_id
    });

    // If no agent type in metadata, try to infer from conversation context
    if (!finalAgentType) {
      console.log('[SAVE_MEETING_WEBHOOK] No agent type in metadata, inferring from content...');

      // Look for agent type hints in the summary or conversation content
      const contentToAnalyze = `${summary} ${action_items || ''} ${research_findings || ''}`.toLowerCase();

      if (contentToAnalyze.includes('marketing') || contentToAnalyze.includes('strategic')) {
        finalAgentType = 'Marketing';
      } else if (contentToAnalyze.includes('research') || contentToAnalyze.includes('analysis')) {
        finalAgentType = 'Research';
      } else if (contentToAnalyze.includes('sales') || contentToAnalyze.includes('revenue')) {
        finalAgentType = 'Sales';
      } else if (contentToAnalyze.includes('software') || contentToAnalyze.includes('development')) {
        finalAgentType = 'SoftwareDesign';
      } else if (contentToAnalyze.includes('business') || contentToAnalyze.includes('process')) {
        finalAgentType = 'BusinessAnalysis';
      }

      if (finalAgentType) {
        console.log('[SAVE_MEETING_WEBHOOK] Inferred agent type from content:', finalAgentType);
      }
    }

    // STEP 2: Map agent type to PMO Agent ID (same as manual save path)
    if (finalAgentType) {
      try {
        const { getTeamIdFromAgentType } = await import('../../../../lib/utils/teamNameUtils');
        const teamId = getTeamIdFromAgentType(finalAgentType);
        mappedAgentId = teamId || 'unknown';

        console.log('[SAVE_MEETING_WEBHOOK] Mapped agent type to PMO Agent ID:', {
          finalAgentType,
          teamId,
          mappedAgentId
        });
      } catch (error) {
        console.error('[SAVE_MEETING_WEBHOOK] Error mapping agent type to PMO ID:', error);
        mappedAgentId = 'unknown';
      }
    } else {
      console.warn('[SAVE_MEETING_WEBHOOK] No agent type found, using unknown');
      mappedAgentId = 'unknown';
    }

    // ✅ STEP 2: Extract metadata for transcript saving - use resolved values
    const agentName = finalAgentType ? `${finalAgentType} Agent` : 'PMO Agent';
    const agentType = finalAgentType || 'PMO';
    const category = selectedDocumentCategory || 'Meeting Transcript';
    const finalDocumentTitle = document_title || `Meeting Summary - ${agentName} - ${new Date().toLocaleDateString()}`;

    console.log('[SAVE_MEETING_WEBHOOK] Using resolved metadata values:', {
      agentName,
      agentType,
      category,
      selectedDocumentCategory,
      finalAgentType,
      mappedAgentId
    });

    // Create transcript metadata
    const transcriptMetadata: TranscriptMetadata = {
      agentName,
      agentType,
      documentTitle: finalDocumentTitle,
      category,
      startTime: new Date(Date.now() - (transcriptMessages.length * 30000)), // Estimate start time
      endTime: new Date(),
      totalMessages: transcriptMessages.length,
      userMessages: transcriptMessages.filter(m => m.role === 'user').length,
      agentMessages: transcriptMessages.filter(m => m.role === 'assistant').length
    };

    console.log('[SAVE_MEETING_WEBHOOK] Saving transcript with metadata:', transcriptMetadata);

    // Save transcript using existing utility with timeout tracking
    console.log('[SAVE_MEETING_WEBHOOK] Starting transcript save process...');
    const saveStartTime = Date.now();

    // Use early-ack pattern: save PDF first, then handle KB upload separately
    const saveResult = await saveTranscript(transcriptMessages, {
      userId: user_id,
      agentId: agent_id || 'unknown', // ✅ Use ElevenLabs agent_id for knowledge base operations
      metadata: transcriptMetadata,
      uploadToKnowledgeBase: false, // Skip KB upload in main flow to avoid timeout
      forceUpload: false,
      onProgress: (step: string, message: string, progress?: number) => {
        const elapsed = Date.now() - saveStartTime;
        console.log(`[SAVE_MEETING_WEBHOOK] Progress (${elapsed}ms) - ${step}: ${message} (${progress || 0}%)`);
      }
    });

    const saveElapsed = Date.now() - saveStartTime;
    console.log(`[SAVE_MEETING_WEBHOOK] PDF generation completed in ${saveElapsed}ms`);

    if (!saveResult.success) {
      console.error('[SAVE_MEETING_WEBHOOK] Failed to save transcript:', saveResult.error);
      return NextResponse.json({
        error: 'Failed to save transcript',
        details: saveResult.error
      }, { status: 500 });
    }

    // Check if we should return early to avoid timeout
    if (shouldReturnEarly()) {
      console.log('[SAVE_MEETING_WEBHOOK] Returning early to avoid timeout, KB upload will continue in background');

      // Start background KB upload (fire and forget)
      if (saveResult.pdfUrl && (agent_id || 'unknown') !== 'unknown') {
        setImmediate(async () => {
          try {
            console.log('[SAVE_MEETING_WEBHOOK] Starting background KB upload...');
            // TODO: Implement background KB upload here
            console.log('[SAVE_MEETING_WEBHOOK] Background KB upload completed');
          } catch (error) {
            console.error('[SAVE_MEETING_WEBHOOK] Background KB upload failed:', error);
          }
        });
      }

      return NextResponse.json({
        success: true,
        message: 'Meeting summary saved successfully',
        pdfUrl: saveResult.pdfUrl,
        fileName: saveResult.fileName,
        note: 'Knowledge base upload continuing in background'
      });
    }

    console.log('[SAVE_MEETING_WEBHOOK] Transcript saved successfully:', {
      pdfUrl: saveResult.pdfUrl,
      fileName: saveResult.fileName,
      knowledgeBaseId: saveResult.knowledgeBaseId
    });

    // Handle document generation if requested
    let documentGenerationResult: MeetingDocumentGenerationResult | null = null;

    if (generate_document) {
      try {
        console.log('[SAVE_MEETING_WEBHOOK] Document generation requested:', {
          title: finalDocumentTitle,
          summary_length: summary.length
        });

        // Create PMO record first
        let pmoId: string | null = null;
        try {
          console.log('[SAVE_MEETING_WEBHOOK] Creating PMO record...');
          pmoId = await createPMORecordFromForm(user_id, {
            title: finalDocumentTitle,
            description: summary,
            priority: 'Medium',
            category: 'Meeting Documents'
          });

          console.log('[SAVE_MEETING_WEBHOOK] PMO record created:', pmoId);
        } catch (pmoError) {
          console.error('[SAVE_MEETING_WEBHOOK] Error creating PMO record:', pmoError);
        }

        // ✅ Determine appropriate category - prioritize selected document category
        let documentCategory: string;

        if (selectedDocumentCategory) {
          // Use the category from selected documents in SelectedDocumentsDisplay
          documentCategory = selectedDocumentCategory;
          console.log('[SAVE_MEETING_WEBHOOK] Using selected document category:', documentCategory);
        } else {
          // Fallback to content-based category determination
          documentCategory = determineMeetingDocumentCategory(
            [],
            summary + (action_items || '')
          );
          console.log('[SAVE_MEETING_WEBHOOK] Using content-based category:', documentCategory);
        }

        // Generate the meeting document with PMO Agent ID (same as transcript save)
        const titleWithPmoId = pmoId ? `[${pmoId}] ${finalDocumentTitle}` : finalDocumentTitle;

        console.log('[SAVE_MEETING_WEBHOOK] Generating document with PMO Agent ID:', {
          finalAgentType,
          mappedAgentId,
          documentCategory,
          titleWithPmoId
        });

        documentGenerationResult = await generateMeetingDocument({
          title: titleWithPmoId,
          category: documentCategory, // ✅ Uses selected document category
          meetingTranscript: transcriptMessages,
          agentId: agent_id || 'unknown', // ✅ Use ElevenLabs agent_id for knowledge base operations
          userId: user_id,
          webSearchResults: web_search_results, // Include web search results from the meeting
          additionalContext: `Meeting Summary: ${summary}${action_items ? `\n\nAction Items: ${action_items}` : ''}${research_findings ? `\n\nResearch Findings: ${research_findings}` : ''}${search_queries.length > 0 ? `\n\nSearch Queries: ${search_queries.join(', ')}` : ''}`,
          pmoId: pmoId || undefined,
          documentId: selectedDocumentId || undefined, // ✅ Use selected document ID if provided
          onProgress: (step: string, message: string, progress?: number) => {
            console.log(`[SAVE_MEETING_WEBHOOK] Document Generation - ${step}: ${message} (${progress || 0}%)`);
          }
        });

        if (documentGenerationResult.success) {
          console.log('[SAVE_MEETING_WEBHOOK] Document generated successfully:', {
            documentId: documentGenerationResult.documentId,
            downloadUrl: documentGenerationResult.downloadUrl,
            knowledgeBaseId: documentGenerationResult.knowledgeBaseId
          });

          // Update PMO record with document details and team assignment
          if (pmoId) {
            try {
              const updateData: any = {
                status: 'Completed',
                summary: `Document generated successfully. Knowledge Base ID: ${documentGenerationResult.knowledgeBaseId || 'N/A'}`
              };

              // Add PMO Agent ID if we have a valid one
              if (mappedAgentId && mappedAgentId !== 'unknown') {
                updateData.agentIds = [mappedAgentId]; // Store PMO Agent ID
              }

              await updatePMORecord(user_id, pmoId, updateData);
              console.log('[SAVE_MEETING_WEBHOOK] PMO record updated with document details');
            } catch (updateError) {
              console.error('[SAVE_MEETING_WEBHOOK] Error updating PMO record:', updateError);
            }
          }
        } else {
          console.error('[SAVE_MEETING_WEBHOOK] Document generation failed:', documentGenerationResult.error);

          // Update PMO record to reflect failure
          if (pmoId) {
            try {
              await updatePMORecord(user_id, pmoId, {
                status: 'Cancelled',
                summary: `Document generation failed: ${documentGenerationResult.error || 'Unknown error'}`
              });
            } catch (updateError) {
              console.error('[SAVE_MEETING_WEBHOOK] Error updating PMO record with failure:', updateError);
            }
          }
        }

      } catch (docGenError) {
        console.error('[SAVE_MEETING_WEBHOOK] Error during document generation:', docGenError);
        documentGenerationResult = {
          success: false,
          error: docGenError instanceof Error ? docGenError.message : 'Unknown error during document generation'
        };
      }
    }

    // Return success response to the agent
    const response = {
      success: true,
      message: 'Meeting summary saved successfully',
      results: {
        transcript: {
          saved: true,
          pdfUrl: saveResult.pdfUrl,
          fileName: saveResult.fileName,
          knowledgeBaseId: saveResult.knowledgeBaseId,
          messageCount: transcriptMessages.length
        },
        documentGeneration: documentGenerationResult
      }
    };

    console.log('[SAVE_MEETING_WEBHOOK] Sending success response:', response);
    return NextResponse.json(response);

  } catch (error) {
    console.error('[SAVE_MEETING_WEBHOOK] Error processing save meeting summary webhook:', error);
    return NextResponse.json({
      error: 'Internal server error',
      details: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 });
  }
}
