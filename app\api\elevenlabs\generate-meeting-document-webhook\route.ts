export const runtime = 'nodejs';
export const maxDuration = 60;

import { NextRequest, NextResponse } from 'next/server';
import {
  generateMeetingDocument,
  determineMeetingDocumentCategory
} from '../../../../lib/tools/meetingDocumentGenerator';
import { TranscriptMessage } from '../../../../components/PMO/TranscriptPanel';
import { createPMORecordFromForm, updatePMORecord } from '../../../../lib/firebase/pmoCollection';
import { getTeamIdFromAgentType } from '../../../../lib/utils/teamNameUtils';
import { v4 as uuidv4 } from 'uuid';

/**
 * Webhook endpoint for ElevenLabs agents to generate documents during meetings
 * This endpoint is called by the voice agent when document generation is requested
 */
export async function POST(request: NextRequest) {
  try {
    console.log('[MEETING_DOC_WEBHOOK] Received document generation request from voice agent');
    console.log('[MEETING_DOC_WEBHOOK] Request headers:', Object.fromEntries(request.headers.entries()));

    // Verify webhook authentication
    const authHeader = request.headers.get('authorization');
    const expectedAuth = `Bearer ${process.env.ELEVENLABS_WEBHOOK_SECRET || 'pmo-webhook-secret-2024'}`;

    // Allow multiple auth tokens for testing and production flexibility
    const validAuthTokens = [
      expectedAuth,
      'Bearer pmo-webhook-secret-2024',
      'Bearer default-secret',
      `Bearer ${process.env.INTERNAL_API_SECRET}`
    ].filter(Boolean);

    if (!authHeader || !validAuthTokens.includes(authHeader)) {
      console.error('[MEETING_DOC_WEBHOOK] Unauthorized webhook request');
      console.error('[MEETING_DOC_WEBHOOK] Received auth:', authHeader);
      console.error('[MEETING_DOC_WEBHOOK] Expected one of:', validAuthTokens);
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Parse request body
    const body = await request.json();
    console.log('[MEETING_DOC_WEBHOOK] Request body:', JSON.stringify(body, null, 2));

    // Extract parameters
    const {
      title,
      category,
      meeting_transcript = [],
      agent_id,
      user_id = 'system',
      web_search_results = [],
      selected_document_categories = [],
      additional_context,
      // Extract transcript metadata for PMO record creation
      transcript_metadata
    } = body;

    console.log('[MEETING_DOC_WEBHOOK] Extracted parameters:', {
      title,
      category,
      transcriptLength: meeting_transcript.length,
      agent_id,
      user_id,
      webSearchResultsCount: web_search_results.length,
      selectedCategories: selected_document_categories
    });

    // Validate required parameters
    if (!title) {
      console.error('[MEETING_DOC_WEBHOOK] Missing required parameters');
      return NextResponse.json({
        error: 'Missing required parameter: title is required'
      }, { status: 400 });
    }

    // Convert meeting transcript to our internal format if provided
    let transcriptMessages: TranscriptMessage[] = [];
    if (meeting_transcript && Array.isArray(meeting_transcript)) {
      transcriptMessages = meeting_transcript.map((msg: any, index: number) => ({
        role: msg.role || msg.speaker || 'assistant',
        content: msg.content || msg.text || msg.message || '',
        timestamp: msg.timestamp ? new Date(msg.timestamp) : new Date(Date.now() - ((meeting_transcript.length - index) * 30000))
      })).filter((msg: TranscriptMessage) => msg.content.trim().length > 0);
    }

    // Extract metadata for PMO record creation
    let agentTeamId = null;
    let finalCategory = 'Meeting Documents'; // Default category
    let documentId = null; // Extract document ID from transcript metadata

    if (transcript_metadata) {
      console.log('[MEETING_DOC_WEBHOOK] Processing transcript metadata:', transcript_metadata);

      // Simple: Use the category directly from transcript metadata
      if (transcript_metadata.documentCategory) {
        finalCategory = transcript_metadata.documentCategory;
      } else if (transcript_metadata.finalCategory) {
        finalCategory = transcript_metadata.finalCategory;
      }

      // Simple: Extract agentType and map to team ID
      const agentType = transcript_metadata.agentType;
      if (agentType) {
        agentTeamId = getTeamIdFromAgentType(agentType);
        console.log('[MEETING_DOC_WEBHOOK] Mapped agentType to teamId:', { agentType, agentTeamId });
      }

      // Extract document ID from transcript metadata
      if (transcript_metadata.documentId) {
        documentId = transcript_metadata.documentId;
        console.log('[MEETING_DOC_WEBHOOK] Using document ID from transcript metadata:', documentId);
      }
    }

    // Fallback to provided category or determined category if no transcript metadata
    if (finalCategory === 'Meeting Documents' && category) {
      finalCategory = category;
    } else if (finalCategory === 'Meeting Documents') {
      finalCategory = determineMeetingDocumentCategory(
        selected_document_categories,
        transcriptMessages.map(m => m.content).join(' ')
      );
    }

    // Create PMO record for this meeting document
    let pmoId: string;
    try {
      console.log('[MEETING_DOC_WEBHOOK] Creating PMO record...');

      pmoId = await createPMORecordFromForm(user_id, {
        title: `Meeting Document: ${title}`,
        description: `Document generated from meeting discussion${additional_context ? `: ${additional_context}` : ''}`,
        priority: 'Medium' as const,
        category: finalCategory,
        customContext: transcriptMessages.length > 0 ?
          `Meeting transcript with ${transcriptMessages.length} messages` :
          'Generated from voice meeting',
        pmoAssessment: `Generated meeting document for ${finalCategory} category${agentTeamId ? ` - Assigned to team: ${agentTeamId}` : ''}`
      });

      console.log('[MEETING_DOC_WEBHOOK] PMO record created:', pmoId);

      // Update PMO record with team assignment if we have a valid team ID
      if (agentTeamId) {
        try {
          await updatePMORecord(user_id, pmoId, {
            agentIds: [agentTeamId],
            teamSelectionRationale: `Team automatically assigned based on agent type: ${transcript_metadata?.agentType || 'unknown'}`,
            status: 'In Progress' as const
          });
          console.log('[MEETING_DOC_WEBHOOK] PMO record updated with team assignment:', agentTeamId);
        } catch (updateError) {
          console.error('[MEETING_DOC_WEBHOOK] Failed to update PMO record with team assignment:', updateError);
        }
      }
    } catch (pmoError) {
      console.error('[MEETING_DOC_WEBHOOK] Failed to create PMO record:', pmoError);
      // Generate a fallback PMO ID to continue with document generation
      pmoId = uuidv4();
    }

    // Update title with PMO ID prefix
    const titleWithPmoId = `[${pmoId}] ${title}`;

    console.log('[MEETING_DOC_WEBHOOK] Generating document:', {
      title: titleWithPmoId,
      category: finalCategory,
      agentTeamId: agentTeamId,
      pmoId: pmoId,
      documentId: documentId
    });

    // Generate the meeting document
    const result = await generateMeetingDocument({
      title: titleWithPmoId,
      category: finalCategory,
      meetingTranscript: transcriptMessages,
      agentId: agentTeamId || undefined, // Only use mapped team ID, pass undefined if not available
      userId: user_id,
      webSearchResults: web_search_results,
      additionalContext: additional_context,
      pmoId: pmoId, // Pass the PMO ID to the document generator
      documentId: documentId || undefined, // Pass the document ID from transcript metadata
      onProgress: (step: string, message: string, progress?: number) => {
        console.log(`[MEETING_DOC_WEBHOOK] Progress - ${step}: ${message} (${progress || 0}%)`);
      }
    });

    if (!result.success) {
      console.error('[MEETING_DOC_WEBHOOK] Document generation failed:', result.error);
      return NextResponse.json({
        error: 'Document generation failed',
        details: result.error
      }, { status: 500 });
    }

    console.log('[MEETING_DOC_WEBHOOK] Document generated successfully:', {
      documentId: result.documentId,
      downloadUrl: result.downloadUrl,
      fileName: result.fileName,
      knowledgeBaseId: result.knowledgeBaseId
    });

    // Return success response with document details
    return NextResponse.json({
      success: true,
      message: 'Document generated successfully',
      document: {
        id: result.documentId,
        title: titleWithPmoId,
        category: finalCategory,
        downloadUrl: result.downloadUrl,
        fileName: result.fileName,
        knowledgeBaseId: result.knowledgeBaseId,
        pmoId: pmoId
      },
      summary: `I've successfully created a document titled "${titleWithPmoId}" in the ${finalCategory} category. The document has been linked to PMO record ${pmoId} and saved to your PMO documents collection.`
    });

  } catch (error) {
    console.error('[MEETING_DOC_WEBHOOK] Error processing document generation webhook:', error);
    return NextResponse.json({
      error: 'Internal server error',
      details: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 });
  }
}
