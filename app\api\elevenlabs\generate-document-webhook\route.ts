import { NextRequest, NextResponse } from 'next/server';
import { createDocumentationAgent } from '../../../../lib/agents/documentation/createDocumentationAgent';
import type { CreateDocumentationAgentOptions } from '../../../../lib/agents/documentation/createDocumentationAgent';

/**
 * Webhook endpoint for ElevenLabs DocumentationGeneration agent
 * This endpoint is called by the voice agent when document generation is requested
 */
export async function POST(request: NextRequest) {
  try {
    console.log('[DOCUMENT_WEBHOOK] Received document generation request from voice agent');

    // Verify webhook authentication
    const authHeader = request.headers.get('authorization');
    const expectedAuth = `Bearer ${process.env.ELEVENLABS_WEBHOOK_SECRET || 'default-secret'}`;
    
    if (authHeader !== expectedAuth) {
      console.error('[DOCUMENT_WEBHOOK] Unauthorized webhook request');
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Parse request body
    const body = await request.json();
    console.log('[DOCUMENT_WEBHOOK] Request body:', JSON.stringify(body, null, 2));

    // Extract parameters from ElevenLabs webhook format
    // ElevenLabs sends the tool parameters in the request body
    const {
      documentType = 'general',
      title,
      requirements,
      context,
      meetingTranscript,
      // Fallback to direct properties if not in tool format
      userId: directUserId,
      agentId: directAgentId
    } = body;

    // For ElevenLabs webhooks, we need to extract userId and agentId from headers or use defaults
    const userId = directUserId || request.headers.get('x-user-id') || 'system-user';
    const agentId = directAgentId || request.headers.get('x-agent-id') || 'documentation-agent';

    // Validate required parameters
    if (!title) {
      console.error('[DOCUMENT_WEBHOOK] Missing required parameters');
      return NextResponse.json({
        error: 'Missing required parameter: title is required'
      }, { status: 400 });
    }

    // Build comprehensive query for document generation
    let query = title;
    if (requirements) {
      query += `\n\nRequirements: ${requirements}`;
    }
    if (context) {
      query += `\n\nContext: ${context}`;
    }
    if (meetingTranscript) {
      query += `\n\nMeeting Discussion:\n${meetingTranscript}`;
      query += `\n\nPlease generate the document based on the above meeting discussion and any relevant context from the knowledge base.`;
    }

    // Determine document category based on type
    const categoryMap: Record<string, string> = {
      'meeting_summary': 'Meeting Documentation',
      'technical_spec': 'Technical Documentation',
      'requirements': 'Requirements Documentation',
      'process': 'Process Documentation',
      'knowledge_base': 'Knowledge Base',
      'general': 'General Documentation'
    };

    const category = categoryMap[documentType] || 'General Documentation';

    // Prepare options for createDocumentationAgent
    const options: CreateDocumentationAgentOptions = {
      userId: userId,
      category: category,
      query: query,
      strategy: 'standalone',
      topK: 10, // Increased for better context
      modelProvider: 'google',
      modelName: 'gemini-2.5-pro',
      generatePDF: true,
      customTitle: title,
      removeTechnicalMetadata: false,
      // PMO integration options
      createPMORecord: true,
      pmoRecordTitle: `Voice Meeting Document: ${title}`,
      // Agent-specific options
      agentId: agentId,
      includeExplanation: false
    };

    console.log('[DOCUMENT_WEBHOOK] Calling createDocumentationAgent with options:', {
      userId,
      category,
      title,
      queryLength: query.length
    });

    // Generate the document
    const result = await createDocumentationAgent.createDocumentation(options);

    if (!result.success) {
      console.error('[DOCUMENT_WEBHOOK] Document generation failed:', result.error);
      return NextResponse.json({
        success: false,
        error: result.error || 'Document generation failed',
        message: 'I apologize, but I encountered an error while generating the document. Please try again or provide more specific requirements.'
      }, { status: 500 });
    }

    console.log('[DOCUMENT_WEBHOOK] Document generated successfully');

    // Prepare response for the voice agent
    const response = {
      success: true,
      message: `I've successfully generated the document "${title}". The document has been created and saved to your knowledge base. ${result.pdfResult?.downloadUrl ? 'A PDF version is also available for download.' : ''}`,
      documentDetails: {
        title: title,
        category: category,
        wordCount: result.markdownContent?.length || 0,
        pdfUrl: result.pdfResult?.downloadUrl || null,
        pmoRecordId: result.pmoRecord?.pmoRecordId || null
      }
    };

    console.log('[DOCUMENT_WEBHOOK] Sending response to voice agent:', {
      success: true,
      title,
      hasPdf: !!result.pdfResult?.downloadUrl
    });

    return NextResponse.json(response);

  } catch (error) {
    console.error('[DOCUMENT_WEBHOOK] Unexpected error:', error);
    
    return NextResponse.json({
      success: false,
      error: 'Internal server error',
      message: 'I apologize, but I encountered an unexpected error while generating the document. Please try again later.'
    }, { status: 500 });
  }
}

// Handle OPTIONS for CORS if needed
export async function OPTIONS(request: NextRequest) {
  return new NextResponse(null, {
    status: 200,
    headers: {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'POST, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type, Authorization',
    },
  });
}
