# ElevenLabs Post-Call Webhook Troubleshooting

## Issue Summary

The Post-Call Webhook to summarize Agent findings during meetings was not triggered after the `end_call` tool was executed by the Agent. This document explains the root cause and provides the solution.

## Root Cause Analysis

### 1. **Missing Workspace-Level Configuration**
- **Issue**: ElevenLabs post-call webhooks must be configured at the **workspace level**, not at the individual agent level
- **Current State**: The webhook endpoint exists and is properly implemented, but <PERSON><PERSON><PERSON><PERSON> doesn't know to call it
- **Solution**: Manual configuration required in ElevenLabs dashboard

### 2. **Authentication Method Mismatch**
- **Issue**: The webhook was expecting Bearer token authentication, but ElevenLabs uses HMAC signature validation
- **Fixed**: Updated webhook to support both HMAC signatures and Bearer tokens for flexibility

### 3. **Data Format Differences**
- **Issue**: The webhook expected a different data structure than what ElevenLabs actually sends
- **Fixed**: Updated data parsing to handle ElevenLabs' actual post-call webhook format

## Solution Implementation

### Approach: Agent-Triggered Webhook Tool

Since the agents are programmatically generated and we cannot configure workspace-level post-call webhooks in the UI, we implemented a **agent-triggered webhook tool** approach similar to the existing `search_web` tool.

### Files Modified

1. **`/app/api/elevenlabs/create-agent/route.ts`**
   - ✅ Added `save_meeting_summary` webhook tool to ALL agents
   - ✅ Tool is configured to be called ONLY when user explicitly requests it
   - ✅ Integrated with existing webhook tool infrastructure

2. **`/app/api/elevenlabs/save-meeting-summary-webhook/route.ts`** (New)
   - ✅ Created webhook endpoint for agent-triggered meeting summaries
   - ✅ Processes summary and action items from agent
   - ✅ Saves transcript and generates documents using existing utilities
   - ✅ Returns success response to agent

3. **`/lib/agents/voice/pmoAgentVoiceConfig.ts`**
   - ✅ Updated agent prompts to inform about the tool
   - ✅ Clear instructions to use tool ONLY when user requests it

4. **`/app/api/elevenlabs/post-call-webhook/route.ts`**
   - ✅ Enhanced for future workspace-level webhook support
   - ✅ Added HMAC signature verification
   - ✅ Updated data parsing for ElevenLabs format

### No Manual Configuration Required

**✅ SOLVED**: This solution works entirely through programmatic agent configuration. No manual ElevenLabs dashboard configuration is needed because:

1. The `save_meeting_summary` tool is added to each agent during creation
2. The tool calls our webhook endpoint when the user requests it
3. The webhook processes the summary and saves the transcript
4. Everything works within the existing agent tool framework

### Environment Variables Required

```bash
# Required for webhook functionality
ELEVENLABS_WEBHOOK_BASE_URL=https://your-domain.com
ELEVENLABS_WEBHOOK_SECRET=pmo-webhook-secret-2024
NEXT_PUBLIC_ELEVENLABS_COMPANY_API_KEY=your_api_key

# For development with ngrok
ELEVENLABS_WEBHOOK_BASE_URL=https://your-ngrok-url.ngrok.io
```

## Webhook Data Flow

### Agent-Triggered Tool Call Format
When the user requests a meeting summary, the agent calls the `save_meeting_summary` tool:

```json
{
  "summary": "Brief summary of key points discussed in the meeting",
  "action_items": "Any action items or next steps identified",
  "document_title": "Optional title for the meeting summary document",
  "generate_document": true,
  "agent_id": "agent_123",
  "conversation_id": "conv_456",
  "user_id": "user_789"
}
```

### Our Internal Processing
1. **Authentication**: Verify Bearer token from agent
2. **Data Extraction**: Extract summary, action items, and metadata
3. **Transcript Creation**: Create structured transcript from summary
4. **Save Transcript**: Use existing `saveTranscript()` utility
5. **Document Generation**: Generate comprehensive meeting document
6. **Knowledge Base Upload**: Upload for future reference
7. **Response**: Return success confirmation to agent

### User Experience
1. User has conversation with agent
2. User says: "Can you prepare a document that summarizes all this information?"
3. Agent calls `save_meeting_summary` tool with conversation summary
4. System saves transcript and generates document
5. Agent confirms: "I've saved the meeting summary and generated a document for you"

## Testing the Fix

### 1. Test with Agent Conversation
1. Start a conversation with your ElevenLabs agent
2. Have a meaningful conversation about a project or topic
3. **Request meeting summary**: Say something like:
   - "Can you prepare a document that summarizes all this information?"
   - "Please save our meeting transcript and create a summary"
   - "I'd like to document our discussion - can you create a meeting summary?"
4. Agent should call the `save_meeting_summary` tool
5. Check server logs for webhook processing
6. Verify transcript and document generation

### 2. Expected Log Output
```
[SAVE_MEETING_WEBHOOK] Received save meeting summary request from voice agent
[SAVE_MEETING_WEBHOOK] Processing meeting summary: {...}
[SAVE_MEETING_WEBHOOK] Created transcript with X messages
[SAVE_MEETING_WEBHOOK] Saving transcript with metadata: {...}
[SAVE_MEETING_WEBHOOK] Transcript saved successfully: {...}
[SAVE_MEETING_WEBHOOK] Document generation requested: {...}
[SAVE_MEETING_WEBHOOK] Document generated successfully: {...}
```

### 3. Verify Results
- Check that a PDF transcript was generated
- Verify the meeting document was created
- Confirm upload to knowledge base
- Agent should confirm successful save to user

## Troubleshooting

### Common Issues

1. **Webhook Not Called**
   - ❌ Workspace-level webhook not configured
   - ✅ Configure in ElevenLabs dashboard

2. **Authentication Errors**
   - ❌ Wrong webhook secret
   - ✅ Check `ELEVENLABS_WEBHOOK_SECRET` environment variable

3. **URL Not Accessible**
   - ❌ Local development URL not public
   - ✅ Use ngrok or deploy to public server

4. **Data Parsing Errors**
   - ❌ Unexpected ElevenLabs format changes
   - ✅ Check logs and update parsing logic

### Debug Commands

```bash
# Test webhook endpoint
curl -X POST https://your-domain.com/api/elevenlabs/post-call-webhook \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer pmo-webhook-secret-2024" \
  -d '{"type":"post_call_transcription","data":{"agent_id":"test","conversation_id":"test","transcript":[]}}'

# Check environment variables
node -e "console.log(process.env.ELEVENLABS_WEBHOOK_BASE_URL)"
```

## Next Steps

1. ✅ Complete manual ElevenLabs dashboard configuration
2. ✅ Test with actual agent conversation
3. ✅ Monitor logs for successful processing
4. ✅ Verify document generation and knowledge base upload
5. ✅ Update documentation with any additional findings

## References

- [ElevenLabs Post-Call Webhooks Documentation](https://elevenlabs.io/docs/conversational-ai/workflows/post-call-webhooks)
- [ElevenLabs Conversational AI Settings](https://elevenlabs.io/app/conversational-ai)
