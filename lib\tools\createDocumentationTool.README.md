# createDocumentationTool

A wrapper tool that encapsulates the entire DB Query workflow for agentic automation. This tool replicates the functionality of the manual DB Query page but can be called programmatically by AI agents.

## Overview

The `createDocumentationTool` provides a unified interface to:
- Execute codebase queries using various strategies
- Generate formatted markdown documentation
- Create professional PDF documents
- Support A/B testing of multiple strategies
- Handle error cases gracefully

## Default Configuration

- **Strategy**: `standalone`
- **topK**: `2`
- **Provider**: `google`
- **Model**: `gemini-2.5-pro`
- **removeTechnicalMetadata**: `true` (clean output for agentic use cases)

## Interface

### CreateDocumentationToolOptions

```typescript
interface CreateDocumentationToolOptions {
  // Required parameters
  userId: string;                    // User identifier
  category: string;                  // Codebase category
  query: string;                     // Natural language query

  // Optional query parameters
  namespaces?: string[];             // Explicit namespaces to query
  strategy?: 'standalone' | 'content-selector' | 'two-stage' | 'hybrid-inverted';
  topK?: number;                     // Results per namespace (default: 2)
  modelProvider?: 'openai' | 'anthropic' | 'groq' | 'google';
  modelName?: string;                // Specific model name
  filters?: Record<string, any>;     // Metadata filters

  // A/B testing parameters
  abTest?: boolean;                  // Enable A/B testing
  abStrategies?: string[];           // Strategies to compare

  // PDF generation parameters
  generatePDF?: boolean;             // Generate PDF document
  customTitle?: string;              // Custom document title

  // Output formatting parameters
  removeTechnicalMetadata?: boolean; // Remove technical sections (default: true)
}
```

### CreateDocumentationToolResult

```typescript
interface CreateDocumentationToolResult {
  success: boolean;
  query: string;
  category: string;
  strategy: string;
  markdownContent: string;           // Formatted markdown output
  rawData: StrategyRun | ABTestResult; // Raw query results
  generatedTitle?: string;           // Generated document title
  pdfResult?: {                      // PDF generation result
    documentId: string;
    downloadUrl: string;
  };
  error?: string;
  metrics?: {                        // Performance metrics
    queryDurationMs: number;
    totalMatches: number;
    namespacesQueried: string[];
  };
}
```

## Output Format Control

The `removeTechnicalMetadata` option controls the level of detail in the output:

### Clean Output (removeTechnicalMetadata: true - Default)
- **Includes**: Only the AI-generated answer content
- **Excludes**: DB Query metadata, strategy details, file paths, scores, code snippets
- **Use Case**: Documentation generation, clean content for end users
- **Example Output**:
```markdown
The authentication system uses JWT tokens for secure user sessions...
```

### Technical Output (removeTechnicalMetadata: false)
- **Includes**: Full query metadata, strategy details, top matches, scores, code snippets
- **Use Case**: Debugging, analysis, detailed technical investigation
- **Example Output**:
```markdown
# DB Query Result

- Mode: single
- Strategy: standalone
- Namespaces: auth, user-management
- Total matches: 15
- Total time: 1250 ms

## Answer

The authentication system uses JWT tokens for secure user sessions...

## Top Matches

- src/auth/jwt.ts — score 0.863
  Implementation of JWT token generation and validation...
```

## Usage Examples

### 1. Basic Documentation Generation

```typescript
import { generateDocumentation } from './createDocumentationTool';

const result = await generateDocumentation(
  '<EMAIL>',
  'Codebase',
  'Summarize the authentication system'
);

if (result.success) {
  console.log('Generated documentation:', result.markdownContent);
}
```

### 2. Advanced Configuration with PDF

```typescript
import { createDocumentationTool } from './createDocumentationTool';

const result = await createDocumentationTool({
  userId: '<EMAIL>',
  category: 'Codebase',
  query: 'Explain the database query system architecture',
  strategy: 'standalone',
  topK: 3,
  generatePDF: true,
  customTitle: 'Database Architecture Guide'
});

if (result.success && result.pdfResult) {
  console.log('PDF URL:', result.pdfResult.downloadUrl);
}
```

### 3. A/B Testing Multiple Strategies

```typescript
const result = await createDocumentationTool({
  userId: '<EMAIL>',
  category: 'Codebase',
  query: 'How does the PMO system work?',
  abTest: true,
  abStrategies: ['standalone', 'content-selector', 'two-stage']
});

if (result.success) {
  const abResult = result.rawData as ABTestResult;
  abResult.runs.forEach(run => {
    console.log(`${run.strategyId}: ${run.quality?.avgScore}`);
  });
}
```

### 4. Filtered Query with Specific Namespaces

```typescript
const result = await createDocumentationTool({
  userId: '<EMAIL>',
  category: 'Codebase',
  query: 'Find React components and their props',
  namespaces: ['frontend', 'components'],
  strategy: 'two-stage',
  filters: {
    filePath: { pattern: '.*\\.tsx?$', flags: 'i' },
    codeEntityType: ['component', 'interface']
  }
});
```

### 5. Control Output Format

```typescript
// Clean documentation output (default)
const cleanResult = await createDocumentationTool({
  userId: '<EMAIL>',
  category: 'Codebase',
  query: 'Explain the payment processing flow',
  removeTechnicalMetadata: true // Only answer content
});

// Technical analysis output
const technicalResult = await createDocumentationTool({
  userId: '<EMAIL>',
  category: 'Codebase',
  query: 'Explain the payment processing flow',
  removeTechnicalMetadata: false // Include all metadata and matches
});

console.log('Clean output:', cleanResult.markdownContent);
console.log('Technical output:', technicalResult.markdownContent);
```

## Core Workflow

The tool replicates the manual DB Query workflow:

1. **Input Validation** - Validates required parameters
2. **Query Execution** - Executes query using specified strategy
3. **Result Formatting** - Converts results to clean markdown
4. **Title Generation** - Generates professional document title
5. **PDF Creation** - Optionally creates PDF document
6. **Metrics Collection** - Gathers performance metrics

## Strategies Available

- **standalone**: Direct query execution (default)
- **content-selector**: Content-aware selection
- **two-stage**: Two-phase filtering approach
- **hybrid-inverted**: Hybrid inverted index strategy

## Error Handling

The tool includes comprehensive error handling:
- Parameter validation
- Query execution errors
- PDF generation failures (non-blocking)
- Network timeouts
- Invalid strategy names

## Integration with Existing Systems

### PMO Document Processing

When `generatePDF: true`, the tool:
1. Generates a professional title using Groq AI
2. Cleans the markdown content (removes technical metadata)
3. Creates a PDF using the existing PMO processing pipeline
4. Stores the document in Firebase with proper metadata

### Metadata Tracking

Generated documents include metadata:
- `source: 'DB Query Tool'`
- `toolGenerated: true`
- `isPMOGenerated: true`
- Query parameters and execution metrics

## Performance Considerations

- Default `topK: 2` for faster execution
- Configurable timeout handling
- Efficient markdown formatting
- Optional PDF generation to avoid blocking

## Testing

Run the test suite:

```typescript
import { runAllTests } from './createDocumentationTool.test';
await runAllTests();
```

## Dependencies

- `lib/services/tools/db-query` - Core query functionality
- `lib/tools/groq-ai` - Title generation
- `lib/pmo/processPMODocument` - PDF generation
- Firebase/Firestore - Document storage

## Future Enhancements

- Batch query processing
- Custom formatting templates
- Integration with other AI models
- Caching for repeated queries
- Real-time progress tracking
