"use client";

import React, { useState, useEffect } from 'react';
import { Button } from '../ui/button';
import {
  Mi<PERSON>,
  MicOff,
  Play,
  Square,
  PhoneOff,
  Volume2,
  VolumeX,
  Activity,
  Loader2,
  AlertCircle,
  User,
  Bot,
  Bug
} from 'lucide-react';
import { PMOAgentVoiceConfig } from '../../lib/agents/voice/pmoAgentVoiceConfig';
import { MeetingState } from './AgentMeetingRoom';
import { usePMOVoiceConversation } from 'hooks/usePMOVoiceConversation';
import SelectedDocumentsDisplay from './SelectedDocumentsDisplay';

interface DocumentContext {
  documents: any[];
  selectedDocuments: string[];
  queuedDocuments: string[];
  isLoading: boolean;
}

interface MeetingInterfaceProps {
  meetingState: MeetingState;
  agentConfig: PMOAgentVoiceConfig | null;
  documentContext?: DocumentContext;
  onStartMeeting: () => void;
  onEndMeeting: () => void;
  onToggleMute: () => void;
  onVolumeChange: (volume: number) => void;
  onTranscriptMessage?: (message: string, isUser: boolean, agentName?: string) => void;
  onCurrentResponse?: (response: string) => void;
  onSpeakingState?: (isSpeaking: boolean, isListening: boolean) => void;
}

export default function MeetingInterface({
  meetingState,
  agentConfig,
  documentContext = { documents: [], selectedDocuments: [], queuedDocuments: [], isLoading: false },
  onStartMeeting,
  onEndMeeting,
  onToggleMute,
  onVolumeChange,
  onTranscriptMessage,
  onCurrentResponse,
  onSpeakingState
}: MeetingInterfaceProps) {
  const [userSpeaking, setUserSpeaking] = useState(false);
  const [agentSpeaking, setAgentSpeaking] = useState(false);
  const [connectionStatus, setConnectionStatus] = useState<'disconnected' | 'connecting' | 'connected'>('disconnected');

  // Voice conversation hook
  const voiceConversation = usePMOVoiceConversation({
    agentConfig,
    agentId: meetingState.agentId,
    documentContext,
    callbacks: {
      onConnect: () => {
        setConnectionStatus('connected');
        onSpeakingState?.(false, true);
        console.log('[MEETING_INTERFACE] Voice conversation connected');
      },
      onDisconnect: () => {
        setConnectionStatus('disconnected');
        onSpeakingState?.(false, false);
        console.log('[MEETING_INTERFACE] Voice conversation disconnected');
      },
      onError: (error) => {
        setConnectionStatus('disconnected');
        onSpeakingState?.(false, false);
        console.error('[MEETING_INTERFACE] Voice conversation error:', error);
      },
      onAgentSpeaking: (speaking) => {
        setAgentSpeaking(speaking);
        onSpeakingState?.(speaking, connectionStatus === 'connected');
      },
      onUserSpeaking: (speaking) => {
        setUserSpeaking(speaking);
      },
      onMessage: (message, isUser) => {
        console.log(`[MEETING_INTERFACE] ${isUser ? 'User' : 'Agent'}: ${message}`);
        onTranscriptMessage?.(message, isUser, agentConfig?.agentName);
      }
    }
  });

  // Simulate voice activity (will be replaced with actual ElevenLabs integration)
  useEffect(() => {
    if (meetingState.isActive) {
      setConnectionStatus('connected');

      // Simulate periodic agent speaking - pause when page is not visible
      const interval = setInterval(() => {
        // Skip simulation if page is not visible to prevent unnecessary state updates
        if (document.visibilityState === 'hidden') {
          return;
        }

        if (Math.random() > 0.8) {
          setAgentSpeaking(true);
          setTimeout(() => setAgentSpeaking(false), 2000);
        }
      }, 5000);

      return () => clearInterval(interval);
    } else {
      setConnectionStatus('disconnected');
    }
  }, [meetingState.isActive]);

  // Update connection status based on meeting state
  useEffect(() => {
    if (meetingState.isConnecting) {
      setConnectionStatus('connecting');
    } else if (meetingState.isActive) {
      setConnectionStatus('connected');
    } else {
      setConnectionStatus('disconnected');
    }
  }, [meetingState.isConnecting, meetingState.isActive]);

  if (!agentConfig) {
    return (
      <div className="flex-1 flex items-center justify-center">
        <div className="text-center text-gray-400">
          <AlertCircle className="h-12 w-12 mx-auto mb-4" />
          <p>No agent configuration available</p>
        </div>
      </div>
    );
  }

  return (
    <div className="flex-1 flex flex-col bg-gray-900 overflow-hidden">
      {/* Meeting Status Bar */}
      <div className="bg-gray-800 px-6 py-3 border-b border-gray-700 flex-shrink-0">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-3">
            <div className={`w-3 h-3 rounded-full ${
              connectionStatus === 'connected' ? 'bg-green-500' :
              connectionStatus === 'connecting' ? 'bg-yellow-500 animate-pulse' :
              'bg-red-500'
            }`}></div>
            <span className="text-sm text-gray-300">
              {connectionStatus === 'connected' ? 'Connected' :
               connectionStatus === 'connecting' ? 'Connecting...' :
               'Disconnected'}
            </span>
            {agentConfig && (
              <span className="text-xs text-gray-500">•</span>
            )}
            {agentConfig && (
              <span className="text-sm text-gray-400">
                {agentConfig.agentName}
              </span>
            )}
          </div>

          {meetingState.connectionError && (
            <div className="flex items-center text-red-400 text-sm">
              <AlertCircle className="h-4 w-4 mr-1" />
              <span className="truncate max-w-xs">{meetingState.connectionError}</span>
            </div>
          )}
        </div>
      </div>

      {/* Main Video Area - Vertical Layout */}
      <div className="flex-1 flex flex-col relative bg-gradient-to-br from-gray-800 to-gray-900">
        {/* Top Row: Controls (left) and Agent Display (center) */}
        <div className="flex-shrink-0 pt-8 pb-4">
          <div className="grid grid-cols-[auto_1fr_auto] items-center justify-items-center gap-6 px-8">
            {/* Controls to the left of the Agent name */}
            <div className="flex justify-end">
              <div className="bg-gray-800 rounded-lg p-4 border border-gray-700 min-w-[360px]">
                {/* Main Controls Row */}
                <div className="flex items-center justify-center space-x-4 mb-3 flex-nowrap">
                  {/* Mute/Unmute Button */}
                  <div className="flex flex-col items-center">
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => {
                        voiceConversation.toggleMute();
                        onToggleMute();
                      }}
                      disabled={!voiceConversation.state.isConnected}
                      className={`w-10 h-10 rounded-full transition-all duration-200 ${
                        voiceConversation.state.isMuted
                          ? 'bg-red-600 hover:bg-red-700 text-white'
                          : 'bg-gray-700 hover:bg-gray-600 text-white'
                      }`}
                    >
                      {voiceConversation.state.isMuted ? (
                        <MicOff className="h-4 w-4" />
                      ) : (
                        <Mic className="h-4 w-4" />
                      )}
                    </Button>
                    <span className="text-xs text-gray-400 mt-1">Mute</span>
                  </div>

                  {/* Start/Stop Button */}
                  <div className="flex flex-col items-center">
                    {voiceConversation.state.isConnected ? (
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={async () => {
                          await voiceConversation.endConversation();
                          onEndMeeting();
                        }}
                        className="w-12 h-12 rounded-full bg-green-600 hover:bg-green-700 text-white shadow-lg"
                      >
                        <Square className="h-5 w-5 text-white" />
                      </Button>
                    ) : (
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={async () => {
                          onStartMeeting();
                          await voiceConversation.startConversation();
                        }}
                        disabled={voiceConversation.state.isConnecting || !voiceConversation.isReady}
                        className="w-12 h-12 rounded-full bg-green-600 hover:bg-green-700 text-white shadow-lg disabled:opacity-50"
                      >
                        {voiceConversation.state.isConnecting ? (
                          <Loader2 className="h-5 w-5 animate-spin text-white" />
                        ) : (
                          <Play className="h-5 w-5 text-white" />
                        )}
                      </Button>
                    )}
                    <span className="text-xs text-white mt-1">
                      {!voiceConversation.isReady ? 'Select Agent' : 'Start'}
                    </span>
                  </div>

                  {/* Disconnect Button */}
                  <div className="flex flex-col items-center">
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={async () => {
                        await voiceConversation.endConversation();
                        onEndMeeting();
                      }}
                      disabled={!voiceConversation.state.isConnected}
                      className="w-10 h-10 rounded-full bg-red-700 hover:bg-red-800 text-white disabled:opacity-50 disabled:bg-gray-600"
                    >
                      <PhoneOff className="h-4 w-4" />
                    </Button>
                    <span className="text-xs text-gray-400 mt-1">Disconnect</span>
                  </div>

                  {/* Debug Audio Button */}
                  <div className="flex flex-col items-center">
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => {
                        console.log('[MEETING_INTERFACE] Debug audio button clicked');
                        voiceConversation.debugAudioOutput();
                      }}
                      className="w-8 h-8 rounded-full bg-gray-700 hover:bg-gray-600 text-white"
                      title="Debug Audio Output"
                    >
                      <Bug className="h-3 w-3" />
                    </Button>
                    <span className="text-xs text-gray-400 mt-1">Debug</span>
                  </div>
                </div>

                {/* Volume Control Row */}
                <div className="flex items-center justify-center space-x-3 pt-2 border-t border-gray-600">
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => {
                      const newVolume = voiceConversation.state.volume > 0 ? 0 : 0.8;
                      voiceConversation.setVolume(newVolume);
                      onVolumeChange(newVolume);
                    }}
                    className="w-8 h-8 rounded-full bg-gray-700 hover:bg-gray-600 text-white"
                  >
                    {voiceConversation.state.volume > 0 ? (
                      <Volume2 className="h-3 w-3" />
                    ) : (
                      <VolumeX className="h-3 w-3" />
                    )}
                  </Button>

                  <div className="flex items-center space-x-2">
                    <input
                      type="range"
                      min="0"
                      max="1"
                      step="0.1"
                      value={voiceConversation.state.volume}
                      onChange={(e) => {
                        const newVolume = parseFloat(e.target.value);
                        voiceConversation.setVolume(newVolume);
                        onVolumeChange(newVolume);
                      }}
                      className="w-20 h-2 bg-gray-600 rounded-lg appearance-none cursor-pointer slider"
                      disabled={!voiceConversation.state.isConnected}
                    />
                    <span className="text-xs text-gray-400 w-8">
                      {Math.round(voiceConversation.state.volume * 100)}%
                    </span>
                  </div>
                </div>

                {/* Meeting Instructions */}
                {!meetingState.isActive && (
                  <div className="text-center mt-3 pt-2 border-t border-gray-600">
                    <p className="text-gray-400 text-xs">
                      Click <strong>Start</strong> to begin
                    </p>
                  </div>
                )}
              </div>
            </div>

            {/* Agent Display (centered) */}
            <div className="flex flex-col items-center text-center justify-self-center">
              {/* Smaller Agent Avatar */}
              <div className={`
                w-20 h-20 rounded-full flex items-center justify-center mb-3 mx-auto
                ${agentConfig?.color || 'bg-purple-600'}
                ${agentSpeaking ? 'animate-pulse ring-4 ring-white/20 scale-105' : ''}
                transition-all duration-300 shadow-xl
              `}>
                <Bot className="h-10 w-10 text-white" />
              </div>

              <h3 className="text-lg font-semibold text-white mb-1">
                {agentConfig?.agentName || 'Agent'}
              </h3>

              {/* Voice Activity Indicator */}
              {agentSpeaking && (
                <div className="flex items-center justify-center space-x-2 bg-green-500/20 rounded-full px-3 py-1 mt-2">
                  <Activity className="h-3 w-3 text-green-400 animate-pulse" />
                  <span className="text-green-400 text-xs font-medium">Speaking...</span>
                </div>
              )}
            </div>

            {/* User Thumbnail (right) aligned with Agent */}
            <div className="flex justify-start">
              <div className="w-32 h-24 bg-gray-800 rounded-lg border-2 border-gray-600 overflow-hidden shadow-lg">
                <div className="h-full flex items-center justify-center relative">
                  <div className={`
                    w-12 h-12 rounded-full bg-gray-700 flex items-center justify-center
                    ${userSpeaking ? 'ring-2 ring-blue-500 bg-blue-600' : ''}
                    ${meetingState.isMuted ? 'bg-red-600' : ''}
                    transition-all duration-300
                  `}>
                    <User className="h-6 w-6 text-white" />
                  </div>

                  {/* User Status Indicator */}
                  <div className="absolute bottom-1 left-1 right-1">
                    <div className="flex items-center justify-center space-x-1 text-xs">
                      {meetingState.isMuted ? (
                        <div className="flex items-center space-x-1 bg-red-600 rounded px-1 py-0.5">
                          <MicOff className="h-3 w-3 text-white" />
                          <span className="text-white text-xs">Muted</span>
                        </div>
                      ) : userSpeaking ? (
                        <div className="flex items-center space-x-1 bg-blue-600 rounded px-1 py-0.5">
                          <Activity className="h-3 w-3 text-white" />
                          <span className="text-white text-xs">Speaking</span>
                        </div>
                      ) : (
                        <div className="flex items-center space-x-1 bg-green-600 rounded px-1 py-0.5">
                          <Mic className="h-3 w-3 text-white" />
                          <span className="text-white text-xs">Ready</span>
                        </div>
                      )}
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Bottom Section - Selected Documents Display with Controls Overlay */}
        <div className="flex-1 px-8 pb-8 overflow-hidden relative">
          {/* Meeting Controls - moved to top grid; hide duplicate bottom block */}
          <div className="hidden">
              {/* Main Controls Row */}
              <div className="flex items-center space-x-4 mb-3 flex-nowrap">
                {/* Mute/Unmute Button */}
                <div className="flex flex-col items-center">
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => {
                      voiceConversation.toggleMute();
                      onToggleMute();
                    }}
                    disabled={!voiceConversation.state.isConnected}
                    className={`w-10 h-10 rounded-full transition-all duration-200 ${
                      voiceConversation.state.isMuted
                        ? 'bg-red-600 hover:bg-red-700 text-white'
                        : 'bg-gray-700 hover:bg-gray-600 text-white'
                    }`}
                  >
                    {voiceConversation.state.isMuted ? (
                      <MicOff className="h-4 w-4" />
                    ) : (
                      <Mic className="h-4 w-4" />
                    )}
                  </Button>
                  <span className="text-xs text-gray-400 mt-1">
                    Mute
                  </span>
                </div>

                {/* Start/Stop Button */}
                <div className="flex flex-col items-center">
                  {voiceConversation.state.isConnected ? (
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={async () => {
                        await voiceConversation.endConversation();
                        onEndMeeting();
                      }}
                      className="w-12 h-12 rounded-full bg-green-600 hover:bg-green-700 text-white shadow-lg"
                    >
                      <Square className="h-5 w-5 text-white" />
                    </Button>
                  ) : (
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={async () => {
                        onStartMeeting();
                        await voiceConversation.startConversation();
                      }}
                      disabled={voiceConversation.state.isConnecting || !voiceConversation.isReady}
                      className="w-12 h-12 rounded-full bg-green-600 hover:bg-green-700 text-white shadow-lg disabled:opacity-50"
                    >
                      {voiceConversation.state.isConnecting ? (
                        <Loader2 className="h-5 w-5 animate-spin text-white" />
                      ) : (
                        <Play className="h-5 w-5 text-white" />
                      )}
                    </Button>
                  )}
                  <span className="text-xs text-white mt-1">
                    {!voiceConversation.isReady ? 'Select Agent' : 'Start'}
                  </span>
                </div>

                {/* Disconnect Button */}
                <div className="flex flex-col items-center">
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={async () => {
                      await voiceConversation.endConversation();
                      onEndMeeting();
                    }}
                    disabled={!voiceConversation.state.isConnected}
                    className="w-10 h-10 rounded-full bg-red-700 hover:bg-red-800 text-white disabled:opacity-50 disabled:bg-gray-600"
                  >
                    <PhoneOff className="h-4 w-4" />
                  </Button>
                  <span className="text-xs text-gray-400 mt-1">
                    Disconnect
                  </span>
                </div>

                {/* Debug Audio Button */}
                <div className="flex flex-col items-center">
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => {
                      console.log('[MEETING_INTERFACE] Debug audio button clicked');
                      voiceConversation.debugAudioOutput();
                    }}
                    className="w-8 h-8 rounded-full bg-gray-700 hover:bg-gray-600 text-white"
                    title="Debug Audio Output"
                  >
                    <Bug className="h-3 w-3" />
                  </Button>
                  <span className="text-xs text-gray-400 mt-1">Debug</span>
                </div>
              </div>

              {/* Volume Control Row */}
              <div className="flex items-center justify-center space-x-3 pt-2 border-t border-gray-600">
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => {
                    const newVolume = voiceConversation.state.volume > 0 ? 0 : 0.8;
                    voiceConversation.setVolume(newVolume);
                    onVolumeChange(newVolume);
                  }}
                  className="w-8 h-8 rounded-full bg-gray-700 hover:bg-gray-600 text-white"
                >
                  {voiceConversation.state.volume > 0 ? (
                    <Volume2 className="h-3 w-3" />
                  ) : (
                    <VolumeX className="h-3 w-3" />
                  )}
                </Button>

                <div className="flex items-center space-x-2">
                  <input
                    type="range"
                    min="0"
                    max="1"
                    step="0.1"
                    value={voiceConversation.state.volume}
                    onChange={(e) => {
                      const newVolume = parseFloat(e.target.value);
                      voiceConversation.setVolume(newVolume);
                      onVolumeChange(newVolume);
                    }}
                    className="w-20 h-2 bg-gray-600 rounded-lg appearance-none cursor-pointer slider"
                    disabled={!voiceConversation.state.isConnected}
                  />
                  <span className="text-xs text-gray-400 w-8">
                    {Math.round(voiceConversation.state.volume * 100)}%
                  </span>
                </div>
              </div>

              {/* Meeting Instructions */}
              {!meetingState.isActive && (
                <div className="text-center mt-3 pt-2 border-t border-gray-600">
                  <p className="text-gray-400 text-xs">
                    Click <strong>Start</strong> to begin
                  </p>
                </div>
              )}
            </div>


          {/* Selected Documents Display - Full width */}
          <SelectedDocumentsDisplay
            documentContext={documentContext}
            maxHeight="100%"
            className="h-full"
          />
        </div>

        {/* Connection Status Overlay */}
        {(meetingState.isConnecting || voiceConversation.state.connectionStatus) && (
          <div className="absolute inset-0 bg-black/60 flex items-center justify-center backdrop-blur-sm">
            <div className="text-center text-white bg-gray-800/80 rounded-lg p-6 max-w-md mx-4">
              <Loader2 className="h-8 w-8 animate-spin mx-auto mb-3 text-purple-400" />
              <p className="text-lg font-medium">
                {voiceConversation.state.connectionStatus || `Connecting to ${agentConfig?.agentName}...`}
              </p>

              {/* Progress Bar */}
              {voiceConversation.state.progress > 0 && (
                <div className="mt-4">
                  <div className="w-full bg-gray-700 rounded-full h-2">
                    <div
                      className="bg-purple-500 h-2 rounded-full transition-all duration-300 ease-out"
                      style={{ width: `${voiceConversation.state.progress}%` }}
                    ></div>
                  </div>
                  <p className="text-xs text-gray-400 mt-2">
                    {voiceConversation.state.progress}% complete
                  </p>
                </div>
              )}

              <p className="text-sm text-gray-400 mt-3">
                {voiceConversation.state.connectionStatus?.includes('document')
                  ? 'Processing documents and setting up knowledge base...'
                  : 'Please wait while we establish the connection'
                }
              </p>
            </div>
          </div>
        )}


      </div>
    </div>
  );
}
