/**
 * Test script to verify the UI context solution
 * 
 * This tests the flow where:
 * 1. DocumentContextPanel.tsx knows the agent type
 * 2. Agent creation passes agentContext with agent type and document categories
 * 3. Conversation initiation passes clientData with context
 * 4. Webhook extracts context from conversation_initiation_client_data
 */

// Mock the getTeamIdFromAgentType function
function mockGetTeamIdFromAgentType(agentType) {
  const agentTypeMapping = {
    'Marketing': 'Ag001',
    'Research': 'Ag002',
    'SoftwareDesign': 'Ag003',
    'Sales': 'Ag004',
    'BusinessAnalysis': 'Ag005',
    'DocumentationGeneration': 'Ag006'
  };
  
  return agentTypeMapping[agentType] || null;
}

// Simulate the UI context flow
function simulateUIContextFlow() {
  console.log('🔄 UI Context Flow Simulation\n');
  
  // Step 1: DocumentContextPanel.tsx has agent type
  const agentType = 'Marketing'; // From DocumentContextPanel.tsx line 159
  const selectedDocuments = [
    {
      id: 'doc1',
      title: 'Marketing Strategy Document',
      category: 'PMO - Single Independent women will be the least prepared for the AI onslaught - 3541080b-ee62-4451-8315-4cd0a0131319',
      content: 'Marketing strategy content...'
    },
    {
      id: 'doc2', 
      title: 'Brand Guidelines',
      category: 'Brand Management',
      content: 'Brand guidelines content...'
    }
  ];
  
  console.log('📋 Step 1: DocumentContextPanel.tsx');
  console.log(`   Agent Type: ${agentType}`);
  console.log(`   Selected Documents: ${selectedDocuments.length}`);
  console.log(`   Primary Category: ${selectedDocuments[0].category}`);
  console.log('');
  
  // Step 2: Agent creation with context
  const selectedDocumentCategories = selectedDocuments
    .map(doc => doc.category)
    .filter(Boolean);
  
  const agentCreationPayload = {
    agentId: '<EMAIL>-pmo-Marketing',
    name: agentType,
    voiceId: '2mltbVQP21Fq8XgIfRQJ',
    prompt: 'Marketing agent prompt...',
    knowledgeBase: selectedDocuments.map(doc => ({
      id: doc.id,
      title: doc.title,
      content: doc.content,
      metadata: doc.metadata
    })),
    // ✅ NEW: Pass agent context for webhook metadata
    agentContext: {
      agentType: agentType,
      documentCategories: selectedDocumentCategories,
      selectedDocuments: selectedDocuments.map(doc => ({
        id: doc.id,
        title: doc.title,
        category: doc.category
      }))
    }
  };
  
  console.log('📋 Step 2: Agent Creation (usePMOVoiceConversation.ts)');
  console.log(`   Agent Context: ${JSON.stringify(agentCreationPayload.agentContext, null, 2)}`);
  console.log('');
  
  // Step 3: Conversation initiation with client data
  const conversationInitiationData = {
    agentId: 'agent_3501k4xhrgnrfm59f92vnzdkybhq', // ElevenLabs agent ID
    clientData: {
      agentType: agentType,
      documentCategory: selectedDocumentCategories.length > 0 ? selectedDocumentCategories[0] : undefined,
      selectedDocuments: selectedDocuments.map(doc => ({
        id: doc.id,
        title: doc.title,
        category: doc.category
      }))
    }
  };
  
  console.log('📋 Step 3: Conversation Initiation (conversation.startSession)');
  console.log(`   Client Data: ${JSON.stringify(conversationInitiationData.clientData, null, 2)}`);
  console.log('');
  
  return {
    agentType,
    selectedDocuments,
    conversationInitiationData
  };
}

// Simulate webhook processing with UI context
function simulateWebhookWithUIContext(conversationInitiationData) {
  console.log('📋 Step 4: Webhook Processing (save-meeting-summary-webhook)');
  
  // Simulate webhook request body with conversation initiation data
  const webhookRequestBody = {
    summary: 'We discussed marketing strategies and brand positioning for the upcoming campaign.',
    agent_id: 'agent_3501k4xhrgnrfm59f92vnzdkybhq',
    user_id: '<EMAIL>',
    transcript_metadata: {}, // Still empty from ElevenLabs
    // ✅ NEW: Conversation initiation client data from UI
    conversation_initiation_client_data: conversationInitiationData.clientData
  };
  
  console.log('   Webhook Request Body:');
  console.log(`     transcript_metadata: ${JSON.stringify(webhookRequestBody.transcript_metadata)}`);
  console.log(`     conversation_initiation_client_data: ${JSON.stringify(webhookRequestBody.conversation_initiation_client_data, null, 2)}`);
  console.log('');
  
  // Extract category with priority logic (conversation initiation data takes priority)
  let selectedDocumentCategory = webhookRequestBody.conversation_initiation_client_data?.documentCategory ||
                                webhookRequestBody.transcript_metadata?.documentCategory || 
                                webhookRequestBody.transcript_metadata?.finalCategory ||
                                webhookRequestBody.transcript_metadata?.category;
  
  // If no full category found, try to extract from other metadata fields
  if (!selectedDocumentCategory) {
    // Try conversation initiation data first
    if (webhookRequestBody.conversation_initiation_client_data?.selectedDocuments && 
        webhookRequestBody.conversation_initiation_client_data.selectedDocuments.length > 0) {
      const firstDoc = webhookRequestBody.conversation_initiation_client_data.selectedDocuments[0];
      selectedDocumentCategory = firstDoc.category;
    }
  }
  
  const metadataAgentType = webhookRequestBody.conversation_initiation_client_data?.agentType || 
                           webhookRequestBody.transcript_metadata?.agentType;
  
  console.log('   Extracted Values:');
  console.log(`     Agent Type: ${metadataAgentType}`);
  console.log(`     Document Category: ${selectedDocumentCategory}`);
  console.log('');
  
  // Map agent type to PMO Agent ID
  let mappedAgentId = 'unknown';
  if (metadataAgentType) {
    const teamId = mockGetTeamIdFromAgentType(metadataAgentType);
    mappedAgentId = teamId || 'unknown';
  }
  
  const category = selectedDocumentCategory || 'Meeting Transcript';
  
  console.log('   Final Results:');
  console.log(`     PMO Agent ID: ${mappedAgentId}`);
  console.log(`     Category: ${category}`);
  console.log('');
  
  return {
    agentId: mappedAgentId,
    category: category,
    success: true
  };
}

// Test the complete flow
async function testUIContextSolution() {
  console.log('🧪 Testing UI Context Solution\n');
  console.log('=' * 80);
  console.log('');
  
  // Simulate the complete flow
  const uiContext = simulateUIContextFlow();
  const webhookResult = simulateWebhookWithUIContext(uiContext.conversationInitiationData);
  
  // Expected results
  const expectedAgentId = 'Ag001';
  const expectedCategory = 'PMO - Single Independent women will be the least prepared for the AI onslaught - 3541080b-ee62-4451-8315-4cd0a0131319';
  
  // Check results
  const agentIdMatch = webhookResult.agentId === expectedAgentId;
  const categoryMatch = webhookResult.category === expectedCategory;
  
  console.log('📊 Final Comparison:');
  console.log(`   Agent ID: "${webhookResult.agentId}" (expected: "${expectedAgentId}") ${agentIdMatch ? '✅' : '❌'}`);
  console.log(`   Category: "${webhookResult.category}" (expected: "${expectedCategory}") ${categoryMatch ? '✅' : '❌'}`);
  
  const overallSuccess = agentIdMatch && categoryMatch;
  console.log(`   Overall: ${overallSuccess ? '✅ PASS' : '❌ FAIL'}`);
  console.log('');
  
  return overallSuccess;
}

// Test solution summary
function testSolutionSummary() {
  console.log('📋 UI Context Solution Summary:\n');
  
  console.log('🔧 Problem:');
  console.log('   ❌ ElevenLabs webhook receives empty transcript_metadata');
  console.log('   ❌ UI context (agent type, document categories) not reaching webhook');
  console.log('');
  
  console.log('🔧 Solution:');
  console.log('   ✅ DocumentContextPanel.tsx already knows agent type (line 159)');
  console.log('   ✅ Pass agentContext during agent creation (usePMOVoiceConversation.ts)');
  console.log('   ✅ Pass clientData during conversation initiation (conversation.startSession)');
  console.log('   ✅ Extract from conversation_initiation_client_data in webhook');
  console.log('');
  
  console.log('🎯 Data Flow:');
  console.log('   1. DocumentContextPanel.tsx: agentType="Marketing"');
  console.log('   2. Agent Creation: agentContext.agentType="Marketing"');
  console.log('   3. Conversation Start: clientData.agentType="Marketing"');
  console.log('   4. Webhook: conversation_initiation_client_data.agentType="Marketing"');
  console.log('   5. Result: agentId="Ag001", category="PMO - Single Independent..."');
  console.log('');
}

// Run tests
async function runTests() {
  console.log('🚀 Testing UI Context Solution\n');
  
  testSolutionSummary();
  
  console.log('=' * 80);
  console.log('');
  
  const success = await testUIContextSolution();
  
  console.log('=' * 80);
  console.log('');
  
  console.log('🎉 Tests completed!');
  console.log('');
  
  if (success) {
    console.log('✅ Solution verified: UI context successfully flows to webhook');
    console.log('   The webhook will now receive correct agent type and document categories');
    console.log('   Files collection will get proper agentId and category values');
  } else {
    console.log('❌ Solution needs refinement');
  }
}

// Execute tests
runTests().catch(console.error);
