/**
 * Create Documentation Agent
 *
 * This agent wraps the createDocumentationTool to provide:
 * 1. Agent-specific logging and error handling
 * 2. Metadata tracking and execution metrics
 * 3. Agent-compatible result format
 * 4. Future extensibility for agent-based enhancements
 */

import { createDocumentationTool, CreateDocumentationToolOptions } from '../../tools/createDocumentationTool';
import { createPMORecordFromForm, updatePMORecord } from '../../firebase/pmoCollection';
import { AgenticTeamId } from '../pmo/PMOInterfaces';
import { adminDb } from '../../../components/firebase-admin';

export interface CreateDocumentationAgentOptions extends CreateDocumentationToolOptions {
  // Agent-specific options
  agentId?: string;
  includeExplanation?: boolean;
  streamResponse?: boolean;
  onStreamUpdate?: (update: CreateDocumentationStreamUpdate) => void;
  // PMO integration options
  createPMORecord?: boolean; // Whether to create a PMO record for tracking
  pmoRecordTitle?: string; // Custom title for the PMO record
}

export interface CreateDocumentationStreamUpdate {
  stage: 'initializing' | 'querying-codebase' | 'generating-content' | 'creating-pdf' | 'complete';
  data?: any;
  message?: string;
  progress?: number; // 0-100
}

export interface CreateDocumentationAgentResult {
  success: boolean;
  query: string;
  category: string;
  strategy: string;
  markdownContent: string;
  rawData: any; // StrategyRun | ABTestResult from the tool
  generatedTitle?: string;
  pdfResult?: {
    documentId: string;
    downloadUrl: string;
  };
  error?: string;
  metrics?: {
    queryDurationMs: number;
    totalMatches: number;
    namespacesQueried: string[];
  };
  // PMO integration results
  pmoRecord?: {
    pmoRecordId: string;
    assignedTeams: string[]; // AgenticTeamId values
  };
  // Agent-specific metadata
  agentMetadata: {
    agentId: string;
    executionId: string;
    startTime: string;
    endTime?: string;
    totalExecutionTimeMs?: number;
    toolExecutionTimeMs?: number;
    agentOverheadMs?: number;
  };
}

/**
 * Helper function to get filenames from namespace IDs
 */
async function getFilenamesFromNamespaces(userId: string, namespaces: string[]): Promise<string[]> {
  if (!namespaces || namespaces.length === 0) {
    return [];
  }

  const filenames: string[] = [];

  try {
    // Query files collection for each namespace
    for (const namespace of namespaces) {
      try {
        const fileSnapshot = await adminDb.collection('users')
          .doc(userId)
          .collection('files')
          .where('namespace', '==', namespace)
          .limit(1)
          .get();

        if (!fileSnapshot.empty) {
          const fileData = fileSnapshot.docs[0].data();
          const filename = fileData.name || `Document-${namespace.substring(0, 8)}`;
          filenames.push(filename);
        }
      } catch (error) {
        console.warn(`Failed to get filename for namespace ${namespace}:`, error);
        // Add a fallback name if we can't get the actual filename
        filenames.push(`Document-${namespace.substring(0, 8)}`);
      }
    }
  } catch (error) {
    console.error('Error getting filenames from namespaces:', error);
  }

  return filenames;
}

export class CreateDocumentationAgent {
  private options: CreateDocumentationAgentOptions;
  private agentId: string;

  constructor(options: CreateDocumentationAgentOptions = {} as CreateDocumentationAgentOptions) {
    this.options = {
      // Set defaults
      strategy: options.strategy || 'standalone',
      topK: options.topK || 5,
      modelProvider: options.modelProvider || 'google',
      modelName: options.modelName || 'gemini-2.5-pro',
      filters: options.filters || {},
      abTest: options.abTest || false,
      generatePDF: options.generatePDF !== false, // Default to true
      // Preserve full content by default in the agent wrapper unless explicitly requested
      removeTechnicalMetadata: options.removeTechnicalMetadata ?? false,
      includeExplanation: options.includeExplanation ?? false,
      streamResponse: options.streamResponse ?? false,
      createPMORecord: options.createPMORecord ?? true, // Default to true for PMO tracking
      pmoRecordTitle: options.pmoRecordTitle,
      ...options
    };

    this.agentId = options.agentId || `doc-agent-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
  }

  /**
   * Create documentation using the wrapped tool
   * @param options - Documentation creation options
   * @returns - Agent result with metadata
   */
  async createDocumentation(options: CreateDocumentationAgentOptions): Promise<CreateDocumentationAgentResult> {
    const executionId = `exec-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
    const startTime = new Date().toISOString();
    const agentStartMs = Date.now();

    try {
      // Validate required parameters
      if (!options.userId || !options.category || !options.query) {
        throw new Error('Missing required parameters: userId, category, and query are required');
      }

      console.log(`CreateDocumentationAgent [${this.agentId}]: Starting documentation creation for query: "${options.query.substring(0, 100)}${options.query.length > 100 ? '...' : ''}"`);

      // Stream update: initializing
      if (this.options.streamResponse && this.options.onStreamUpdate) {
        this.options.onStreamUpdate({
          stage: 'initializing',
          message: 'Initializing documentation creation agent...',
          progress: 0
        });
      }

      // Merge options with agent defaults (keeping agent-specific flags)
      const agentOptions: CreateDocumentationAgentOptions = {
        ...this.options,
        ...options
      } as CreateDocumentationAgentOptions;

      // Build tool options by forwarding every tool parameter explicitly
      const toolOptions: CreateDocumentationToolOptions = {
        userId: agentOptions.userId,
        category: agentOptions.category,
        query: agentOptions.query,
        namespaces: agentOptions.namespaces,
        strategy: agentOptions.strategy,
        topK: agentOptions.topK,
        modelProvider: agentOptions.modelProvider,
        modelName: agentOptions.modelName,
        filters: agentOptions.filters,
        abTest: agentOptions.abTest,
        abStrategies: agentOptions.abStrategies,
        generatePDF: agentOptions.generatePDF,
        customTitle: agentOptions.customTitle,
        removeTechnicalMetadata: agentOptions.removeTechnicalMetadata
      };

      console.log(`CreateDocumentationAgent [${this.agentId}]: Using configuration:`, {
        strategy: toolOptions.strategy,
        modelProvider: toolOptions.modelProvider,
        modelName: toolOptions.modelName,
        generatePDF: toolOptions.generatePDF,
        abTest: toolOptions.abTest
      });

      // Stream update: querying codebase
      if (this.options.streamResponse && this.options.onStreamUpdate) {
        this.options.onStreamUpdate({
          stage: 'querying-codebase',
          message: 'Querying codebase for relevant information...',
          progress: 25
        });
      }

      // Call the underlying tool
      const toolStartMs = Date.now();
      console.log(`CreateDocumentationAgent [${this.agentId}]: Calling createDocumentationTool...`);
      
      const toolResult = await createDocumentationTool(toolOptions);
      
      const toolEndMs = Date.now();
      const toolExecutionTimeMs = toolEndMs - toolStartMs;

      console.log(`CreateDocumentationAgent [${this.agentId}]: Tool execution completed in ${toolExecutionTimeMs}ms`);

      if (!toolResult.success) {
        throw new Error(`Documentation tool failed: ${toolResult.error}`);
      }

      // Stream update: generating content
      if (this.options.streamResponse && this.options.onStreamUpdate) {
        this.options.onStreamUpdate({
          stage: 'generating-content',
          message: 'Processing and formatting documentation content...',
          progress: 75
        });
      }

      // Stream update: creating PDF (if applicable)
      if (toolOptions.generatePDF && this.options.streamResponse && this.options.onStreamUpdate) {
        this.options.onStreamUpdate({
          stage: 'creating-pdf',
          message: 'Generating PDF document...',
          progress: 90
        });
      }

      // PMO Record Integration
      let pmoRecordResult: { pmoRecordId: string; assignedTeams: string[] } | undefined;

      if (agentOptions.createPMORecord) {
        try {
          console.log(`CreateDocumentationAgent [${this.agentId}]: Creating PMO record for documentation tracking...`);

          // Get filenames from namespaces for contextFiles
          const namespacesQueried = toolResult.metrics?.namespacesQueried || [];
          const contextFiles = await getFilenamesFromNamespaces(options.userId, namespacesQueried);

          // Create PMO record
          const pmoTitle = agentOptions.pmoRecordTitle || toolResult.generatedTitle || `Documentation: ${options.query.substring(0, 50)}${options.query.length > 50 ? '...' : ''}`;

          const pmoRecordId = await createPMORecordFromForm(options.userId, {
            title: pmoTitle,
            description: `Documentation generation request: ${options.query}`,
            priority: 'Medium',
            category: agentOptions.category, // Use the actual category from the request
            sourceFile: `Generated by CreateDocumentationAgent`,
            fileName: `${pmoTitle} - Documentation`,
            customContext: `Agent ID: ${this.agentId}\nStrategy: ${toolOptions.strategy}\nModel: ${toolOptions.modelProvider}/${toolOptions.modelName}\nNamespaces: ${namespacesQueried.join(', ')}`,
            pmoAssessment: `Automated documentation generation completed successfully. Generated ${toolResult.markdownContent?.length || 0} characters of content.${toolResult.pdfResult ? ' PDF document created.' : ''}\n\nFiles processed: ${contextFiles.join(', ')}`,
            selectedCategory: agentOptions.category, // Set contextCategories to match category
          });

          // Assign DocumentationGeneration team and update contextFiles
          const assignedTeams = [AgenticTeamId.DocumentationGeneration];

          await updatePMORecord(options.userId, pmoRecordId, {
            agentIds: assignedTeams,
            teamSelectionRationale: "DocumentationGeneration team automatically assigned for all documentation generation activities. This team specializes in creating comprehensive documentation using AI-powered tools and agents.",
            status: 'Completed',
            contextFiles: contextFiles.length > 0 ? contextFiles : null, // Set the actual filenames
            contextCategories: [agentOptions.category] // Ensure contextCategories matches the category
          });

          pmoRecordResult = {
            pmoRecordId,
            assignedTeams: assignedTeams.map(team => team.toString())
          };

          console.log(`CreateDocumentationAgent [${this.agentId}]: PMO record created successfully with ID: ${pmoRecordId}`);
        } catch (pmoError: any) {
          console.warn(`CreateDocumentationAgent [${this.agentId}]: Failed to create PMO record:`, pmoError);
          // Continue without failing the entire operation
        }
      }

      const endTime = new Date().toISOString();
      const agentEndMs = Date.now();
      const totalExecutionTimeMs = agentEndMs - agentStartMs;
      const agentOverheadMs = totalExecutionTimeMs - toolExecutionTimeMs;

      console.log(`CreateDocumentationAgent [${this.agentId}]: Documentation creation completed successfully`);
      console.log(`CreateDocumentationAgent [${this.agentId}]: Execution metrics:`, {
        totalExecutionTimeMs,
        toolExecutionTimeMs,
        agentOverheadMs,
        generatedTitle: toolResult.generatedTitle,
        contentLength: toolResult.markdownContent?.length || 0,
        pdfGenerated: !!toolResult.pdfResult,
        pmoRecordCreated: !!pmoRecordResult
      });

      // Stream update: complete
      if (this.options.streamResponse && this.options.onStreamUpdate) {
        this.options.onStreamUpdate({
          stage: 'complete',
          message: 'Documentation creation completed successfully!',
          progress: 100,
          data: {
            title: toolResult.generatedTitle,
            contentLength: toolResult.markdownContent?.length || 0,
            pdfGenerated: !!toolResult.pdfResult,
            pmoRecordCreated: !!pmoRecordResult
          }
        });
      }

      // Return agent result with metadata
      const agentResult: CreateDocumentationAgentResult = {
        ...toolResult, // Include all tool result properties
        pmoRecord: pmoRecordResult, // Include PMO record information
        agentMetadata: {
          agentId: this.agentId,
          executionId,
          startTime,
          endTime,
          totalExecutionTimeMs,
          toolExecutionTimeMs,
          agentOverheadMs
        }
      };

      return agentResult;

    } catch (error: any) {
      const endTime = new Date().toISOString();
      const agentEndMs = Date.now();
      const totalExecutionTimeMs = agentEndMs - agentStartMs;

      console.error(`CreateDocumentationAgent [${this.agentId}] error:`, error);

      // Stream update: error
      if (this.options.streamResponse && this.options.onStreamUpdate) {
        this.options.onStreamUpdate({
          stage: 'complete',
          message: `Error: ${error.message}`,
          progress: 100
        });
      }

      return {
        success: false,
        query: options.query || '',
        category: options.category || '',
        strategy: options.strategy || 'standalone',
        markdownContent: '',
        rawData: {},
        error: error.message || "Unknown error occurred in CreateDocumentationAgent",
        agentMetadata: {
          agentId: this.agentId,
          executionId,
          startTime,
          endTime,
          totalExecutionTimeMs,
          toolExecutionTimeMs: 0,
          agentOverheadMs: totalExecutionTimeMs
        }
      };
    }
  }
}

// Export a singleton instance for convenience
export const createDocumentationAgent = new CreateDocumentationAgent();
